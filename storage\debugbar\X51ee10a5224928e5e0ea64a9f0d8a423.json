{"__meta": {"id": "X51ee10a5224928e5e0ea64a9f0d8a423", "datetime": "2025-07-30 09:30:02", "utime": **********.934532, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.299491, "end": **********.934556, "duration": 0.6350650787353516, "duration_str": "635ms", "measures": [{"label": "Booting", "start": **********.299491, "relative_start": 0, "end": **********.837849, "relative_end": **********.837849, "duration": 0.5383579730987549, "duration_str": "538ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.837866, "relative_start": 0.5383751392364502, "end": **********.934559, "relative_end": 3.0994415283203125e-06, "duration": 0.09669303894042969, "duration_str": "96.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46674176, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=951\" onclick=\"\">app/Http/Controllers/FinanceController.php:951-1018</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01177, "accumulated_duration_str": "11.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.890065, "duration": 0.009439999999999999, "duration_str": "9.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 80.204}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.910798, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 80.204, "width_percent": 6.032}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 967}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.917757, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:967", "source": "app/Http/Controllers/FinanceController.php:967", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=967", "ajax": false, "filename": "FinanceController.php", "line": "967"}, "connection": "radhe_same", "start_percent": 86.236, "width_percent": 6.032}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 991}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9225109, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:991", "source": "app/Http/Controllers/FinanceController.php:991", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=991", "ajax": false, "filename": "FinanceController.php", "line": "991"}, "connection": "radhe_same", "start_percent": 92.268, "width_percent": 7.732}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-536481009 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-536481009\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1888438181 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888438181\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1318747064 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1318747064\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlpqZ2lWZDM0UFBOQ0loY0ZPVk5SY0E9PSIsInZhbHVlIjoiTldkRHRGUHBzb1JIdlVtSEY3Smw0bkZLT2ZaWFRTYjA4ZjBTU25aVzRjSVVUbjVWbmlsMFBnVndnbUdNc21xOVFrM2pYWnZ6VlRxZHJzdXBGUVI3bzU2MEhtRGdyZTdKWlhaSnNJVzdLL3lTWkpndUdqSEZsY3lXRUJ1Q1RLTUM1cXFPcVBOTis4WWhOK1lrNGQrdE1RR2IxNkE4QmM0ZFdwREtzZmQ4elZMZG9IR3dDY0UxNWVlaFhCcFY2L0JGNGVoY2lKNEJYbWszT0NHM2pnVEVUTVc2TzZqTjZNRGE0WVUzc28vSytqMnhVOXFpTE5XeHRNbnV6QUxkQjJlMytPdmQ0OGNsQlRiOGQxeDQzekdzaWhhRTNoSmV4TFNnV0FGb2hTYVUwUE5mNWwwKzNMSkx2ZTl0WlFjcWUxUWRYSG13MGZlYTVFTTBQNTMxbU1VN2RVZnFQQ2Z4eDhLMXU5SHJQVWZMVm1iMmdiMkR4RFZTSTdHVFlZd0N6NkhIVi9aajlCUFVyRC9VSEFHM1BDNFlKMFg5dEhia3RlTjZzNHFFMkFiYWJhUXI3Q0tmZGVHVDFVeU9BUHpqTUhIYU12WlJxRDV3WlROSnpnNVVpanVkOU5ycmJZTmlrOG1QU2tjU1B2dndHUFlWUW5JU1YyajhVaHVtbXVGRllXa2oiLCJtYWMiOiI2ZTFmNWZmOGVkMjljYThiNzkxZjJkMzliYjk1Y2U0MjAyZjlhNDA4MjRiNDhlYmUwOGNlNTAxODdjMDQyNjc3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlNGY3RVSUg4d0duRExqd3pabFBmSHc9PSIsInZhbHVlIjoiR1RjWDZXUnc0YnNTcitzSHk1NVlocWhleGt3aTdlUkZVd0JlWmJJMEJWbXMreVpnampsczJzV1pWZW9KQXVUUzF6SXo4WUMxd3BnN2lLclh6blZuNTRsV0llWGFYeWtPcC9FTCtiTUtqU2JtNkFRaHpEcUk0dWk2ME82SmtYNGNTNEtPYjkxTytSMmdNZzFWeDhMbW5qdnRiQ2hNY1NhajBsbHk3VVZpeEdldmNraEVGdk1TcDBhRStjV084TEdRSjdTZkk0MDhobW5lV2pNbXZrK3k2TU4reFRTZkd5SUF5U1FEc2NKc2c4ZW1RYVRoRk5lRWZacUxCM3BJL2ZXSzByOEwwazhCWkIwa2ppdjg0TFVXNEhQYnNpbVJONEZiYTNWclVqdFpUK0NoaDNBcjVaSkszSStERXdyM3JMNnFSNUgxMmhoMTR4V3NLTFFtZlViSTM4R3JBQ1BWV3hqYkF5Qk5IV0FIYXZjRmQ3Wm0vWDF2YTIvdzFOWEYvdTdYbFJ6Y0plcEo1OXU1OHlaSkRSUDZLbTJCYTB4dnFYOFlzNTdjTVpXWXRCaTdZbDFhUVdzNno2NldUdUxpbkRtd292WjJOTHExbzlTNldxQi9VcVRZTS95RC90c0RCMy9OaEU1Zzl4emFvdTc3WHNUbjM2d0RLYzY1MVAxb2NYMEkiLCJtYWMiOiJlY2I3NmJhODFmNzVkOWU5NDQyODcwY2Q2NTQ3ZThhZWZlOTYyN2YwNGRiYTc5YjE5OGE0MmRmOGNkZWY1NTQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1961934560 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1961934560\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-346632448 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:30:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZEUHdUQjlVeUgrV1crbkNvSVhyZ3c9PSIsInZhbHVlIjoibkFGUmF4U3BVbjBGK1hidG1YNmdqYzFZQmRlSmdCQUN6Vld6MU5KUEVLMmZsWmc1NXBlaC9WdWc4VzBnNlEzRzZUNnQ5MGduZ2IyS2c2bnUwMWFycTB6MCt4QVY0bHdTcnRyMSt5eWJSM3pjWk9ybjZ1em1xVHpZdERMQVVtTHRGSFBGcUZQSVZSaGRxcVBCZ2RaZXRPUU1hU3VEYXNMdWxoWXVZbUpWVXVHbVkwaThWd1hkMGZsOUZjK0JkNXRGUW9NTWJiaVIrYWZyaTB6dkpsUzduK3QyWktKWnRaWElZZnhHOGNTMmllSkZZYnV0eDlpTkNoTDlMdXErRHFtQjY0azhkcnVIa2loVm5heWZ3OG8wSFBBVHF4WDlvNVlLd1U1cDlaQ1dVakdCTEZhaVByRktGMTJqYnhvSE9ZOGtRSGRZUnZyYVVkZm1mOUJMWDhORDUraVVnTXZOd1FBL2RQZVo3bU43eCt3VU9XTVU3NFlGTlh0ZDVEdXEzQnptZXVsZEhzZFFtQ2xXaWI1cGtzaGNUbkFTWkYvMUphR0dITnNzNjNHNG9uWXJFR01mMnF4SFA2M25Kd3RpaUdlZ09HRXlScWVaK2JGZy9adm8wN3p0K3BaTlZVYTZzRTlWelQ4eVVyK1BFcjZBR09UK1psSHZXcjAvYUdDbWNqMnMiLCJtYWMiOiJlZWY4MGYxNzUxOWI2ZjE0YzQxNTkwZDc2YTc0YmQyNTNmNGNlNzU3NmE2NzRjYzA2NjBhNWE5ZTYxNDMxNDgzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:30:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IitJT0VPekxmQ3p6OGoyQlBoclV4M1E9PSIsInZhbHVlIjoiVnJrOEM0UmxDRFBPZzQydjdvNGdjRDBMSzNINVJzREJ4T2xpS0k0MzFxdEZJNnhyYUhoTjBRYlY2OVFkN0UzT3FWZnZOWEFyeUo4R2xYVnFwWXBzT3NZSEN6b0FjQk1yOGdRWHd2WVZ1aFp5R3ZzRS9nOXZiWm1mT0pNN2NSa2NOdU9FeGdlUlUvNks4aHo5MjV5TUZBRTllSGNkTXNWQ0VEcHpMV25QWkZlWVlFMDFPbVlXdVIvMC9nMUo0OTExQWVWbDkwblhTQzZKaFNacTBmQWNyY2xrSmxXTlBGdkg2WU0wRlY1TDNZSkVEY0JCZ3FVczV3YjgraDJqV0dXYUdDS3R5dDRuaEtGRFFvdS93Tll0T2hqbjZtT0JzaHVObGZlQlFlWktsbjk2VjF3N3JoTHhVaElldjJkdFA4OER4WHhxcjVEMktVVEpqNW5mYnJERGI3S3NobVhDMzVqOXlXMTJTOTA2eittektMdUpscDl1d0xkdW1MMDB3RW9VUmw0QUprK1NmczRLclU4c2FWRlJnVTF4bTM1dmxPeCtPSEJTUkxvOTBYcVNWNXFuRTR6Z0VxZXdDKzB3OXBPb2FjNFVjMkJoL1M4SEEyVnFVb2hFaDhzc3FpWW1xUEFsbm5kcGM4K3NMdWl6enV1Z2JwaHptMit2NGNGSlVHZUciLCJtYWMiOiJmYjc0YTcxZDZmMzRlYjA3NDhkZGZkNjJhMzM0ODZmYTZiNzlkNDNjNGU5MjFlM2RmOWY1YWVlZDg1MjI0ODY3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:30:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZEUHdUQjlVeUgrV1crbkNvSVhyZ3c9PSIsInZhbHVlIjoibkFGUmF4U3BVbjBGK1hidG1YNmdqYzFZQmRlSmdCQUN6Vld6MU5KUEVLMmZsWmc1NXBlaC9WdWc4VzBnNlEzRzZUNnQ5MGduZ2IyS2c2bnUwMWFycTB6MCt4QVY0bHdTcnRyMSt5eWJSM3pjWk9ybjZ1em1xVHpZdERMQVVtTHRGSFBGcUZQSVZSaGRxcVBCZ2RaZXRPUU1hU3VEYXNMdWxoWXVZbUpWVXVHbVkwaThWd1hkMGZsOUZjK0JkNXRGUW9NTWJiaVIrYWZyaTB6dkpsUzduK3QyWktKWnRaWElZZnhHOGNTMmllSkZZYnV0eDlpTkNoTDlMdXErRHFtQjY0azhkcnVIa2loVm5heWZ3OG8wSFBBVHF4WDlvNVlLd1U1cDlaQ1dVakdCTEZhaVByRktGMTJqYnhvSE9ZOGtRSGRZUnZyYVVkZm1mOUJMWDhORDUraVVnTXZOd1FBL2RQZVo3bU43eCt3VU9XTVU3NFlGTlh0ZDVEdXEzQnptZXVsZEhzZFFtQ2xXaWI1cGtzaGNUbkFTWkYvMUphR0dITnNzNjNHNG9uWXJFR01mMnF4SFA2M25Kd3RpaUdlZ09HRXlScWVaK2JGZy9adm8wN3p0K3BaTlZVYTZzRTlWelQ4eVVyK1BFcjZBR09UK1psSHZXcjAvYUdDbWNqMnMiLCJtYWMiOiJlZWY4MGYxNzUxOWI2ZjE0YzQxNTkwZDc2YTc0YmQyNTNmNGNlNzU3NmE2NzRjYzA2NjBhNWE5ZTYxNDMxNDgzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:30:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IitJT0VPekxmQ3p6OGoyQlBoclV4M1E9PSIsInZhbHVlIjoiVnJrOEM0UmxDRFBPZzQydjdvNGdjRDBMSzNINVJzREJ4T2xpS0k0MzFxdEZJNnhyYUhoTjBRYlY2OVFkN0UzT3FWZnZOWEFyeUo4R2xYVnFwWXBzT3NZSEN6b0FjQk1yOGdRWHd2WVZ1aFp5R3ZzRS9nOXZiWm1mT0pNN2NSa2NOdU9FeGdlUlUvNks4aHo5MjV5TUZBRTllSGNkTXNWQ0VEcHpMV25QWkZlWVlFMDFPbVlXdVIvMC9nMUo0OTExQWVWbDkwblhTQzZKaFNacTBmQWNyY2xrSmxXTlBGdkg2WU0wRlY1TDNZSkVEY0JCZ3FVczV3YjgraDJqV0dXYUdDS3R5dDRuaEtGRFFvdS93Tll0T2hqbjZtT0JzaHVObGZlQlFlWktsbjk2VjF3N3JoTHhVaElldjJkdFA4OER4WHhxcjVEMktVVEpqNW5mYnJERGI3S3NobVhDMzVqOXlXMTJTOTA2eittektMdUpscDl1d0xkdW1MMDB3RW9VUmw0QUprK1NmczRLclU4c2FWRlJnVTF4bTM1dmxPeCtPSEJTUkxvOTBYcVNWNXFuRTR6Z0VxZXdDKzB3OXBPb2FjNFVjMkJoL1M4SEEyVnFVb2hFaDhzc3FpWW1xUEFsbm5kcGM4K3NMdWl6enV1Z2JwaHptMit2NGNGSlVHZUciLCJtYWMiOiJmYjc0YTcxZDZmMzRlYjA3NDhkZGZkNjJhMzM0ODZmYTZiNzlkNDNjNGU5MjFlM2RmOWY1YWVlZDg1MjI0ODY3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:30:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-346632448\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1771801699 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771801699\", {\"maxDepth\":0})</script>\n"}}