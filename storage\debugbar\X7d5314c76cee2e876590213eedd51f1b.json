{"__meta": {"id": "X7d5314c76cee2e876590213eedd51f1b", "datetime": "2025-07-30 09:11:37", "utime": **********.961281, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.300043, "end": **********.961302, "duration": 0.6612589359283447, "duration_str": "661ms", "measures": [{"label": "Booting", "start": **********.300043, "relative_start": 0, "end": **********.867384, "relative_end": **********.867384, "duration": 0.5673408508300781, "duration_str": "567ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.867426, "relative_start": 0.5673828125, "end": **********.961305, "relative_end": 2.86102294921875e-06, "duration": 0.09387898445129395, "duration_str": "93.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46686216, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-972</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00876, "accumulated_duration_str": "8.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.920206, "duration": 0.0064800000000000005, "duration_str": "6.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 73.973}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9399269, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 73.973, "width_percent": 8.562}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.945027, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:921", "source": "app/Http/Controllers/FinanceController.php:921", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=921", "ajax": false, "filename": "FinanceController.php", "line": "921"}, "connection": "radhe_same", "start_percent": 82.534, "width_percent": 7.991}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 945}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.949332, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:945", "source": "app/Http/Controllers/FinanceController.php:945", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=945", "ajax": false, "filename": "FinanceController.php", "line": "945"}, "connection": "radhe_same", "start_percent": 90.525, "width_percent": 9.475}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1855972711 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1855972711\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1928309049 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1928309049\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2013199230 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2013199230\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-802589118 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlpSZTlLNzBhSkdpRTZ5Y2taaFgyV1E9PSIsInZhbHVlIjoiQlFvVGM4SWZ3UzNacGZLSFZpWEFtSkZVb0F0dlFlTERDQUx1clFZSDlkNlIxcDJmSE5xQzAwT2NTQ0hxby9ycTdHUWpYajVMdXFIQVZtQnk3WUdsaVNoaTl0N1kwRW1pT3RxTmdCb20zYTlPcGN5aWlUV2NxVFB5ZEFsQWZHR0F5bGZ2bW9RU0VXZnNXcDQ0K1IrMTkwYUhXWTEvclhUdFZOS3VsZFE0TGIxWWRiRmh4Z3FWK21OK1dveEY2R2I0L2FlWEhBaHZXV3QzQ1doS01UYkFIdFYrMWNUVy9yTnczM3ZQRHFLcWM0VWJhMGJRRHNJRE1uOUtTNld0djBXVnMrZW9wTUl3NVY1bStSLzQ1NzdNNkIyNFhOMVE2YmVhNXNja1crQjlJWUxGWVM3UzdlR044NFBPN3ovcXpSV0cxWjM4Z0FrdlQ1YmNOL2EwazVIZ2lPWjIyK2FBVEhMY2s3Undra2VXeGNUSGdWQWVraXFTMTlGaFFDeThJTFhjOTh5MFVsU3RjaUViRnJKYXZGTFZEOHk0bi8rTlNsMDROTFlWbWNkK1ptU0Z2NE0zcDhNN3FpUGxVaTY0aVNKVXhDYTFRQVZ0R0dla3U1MFdTZWJ5bnJWRTI3MGE2V1NEY0dJYVIvRm9vbVN3SjFwdEhiazI5enEzWTAyQVFIZ1EiLCJtYWMiOiJkZWI4Y2NiYmMwZTNmNmZkNWU2Y2ZmZWI1YmExN2VjNDFmMGRiZTU1YzIyMmExYjU5Mzg3YjVmNzIzZjkxODY4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlJuV1M0b091WUJQb1pTK1ZlQ21Panc9PSIsInZhbHVlIjoiczV5WkZSOTJWeUNrOFM4KzhKcUI1M211TjFINUpCa1d5MkJiV1U1Zm1ObTF5V2tYRnQxK053YU9BYVRSY2M1dlU3azM1WFp0TmRCYXNVOTQ5UnliOGEvcG5PS2IzZE10N2kxUmcrR0NnRXRtNEFvc2c3VnluVGc3cUxiWkw5aEsyWHQ3RHV4azg1L1dEVE4wbG1odE5qQWtHOXVmaktlUk4vdEdtbUw2WDkyZTlsNThNM3BJdTcvZVBKOFZ0aWdpaUIwNTFVNzVnbWVScEZBOEQzc0RnMlNxZmw3dG5jdmQ2QitJSEd4TVk5QiszaXRQZmVkTW1RK1JLV21WOXdyUVg0bWdWNTZmbTlvMjdQL3RId0VybFJBcTR1ZXNRTDVyR1UwQ0RzejRaVDBmQkxzWTc5VUZvdC9VVi9WQ1V4WjBOTW5mcHQ0akh5RjV1VHpyNUtUWjVobS8wSFd4L2J3NG1xT2o1OVpuNnlHbVdjNVJzNWh1REhsNG8wMlFOWVdJU0VOb0NUVVNvUldJZjAra0d2VEVEL1ppcUVJVEkxeXlqUnFTZk1TY0VJeHdyQ2ExejBybVFUdjJiVDl5aE9KMkc1UW9kVk9vbzNCK1VybHk3UUZ0UCtoMWZSU3c3ZXNua3FpUk10S1NuYXJDdWljY1ZoYXRJS3dZSTVMS2o2REYiLCJtYWMiOiJhYzM2MDQzYTExYzVhZjAzOTYzZGRjNDU0ZTc3Yjg5MjkwY2EyNmNmNzY4MTA0YzQ4NDY5YWUxZmMyOTRkYjgwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802589118\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-391676042 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391676042\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-769011107 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:11:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldYMkJJS0NSY01jUk5vQWljaUVqY2c9PSIsInZhbHVlIjoiTVNiYjVYNkZtdnJ3aWdiTFR4aWNmazFQeGZRTzlGM1Q2TnRCZ3Z0K0ptK1BkZmFvY2N2OVlPZWxMSmYyaHpEUlk4UGJrazRmSWZiWXdJNU5nSnRXckV1K3hWTlg2M0I4cE1oYVVuTVp2OTBVYi8vSWQ3T0U1SjE3MDZCQVZONlRBemMzYmlPc0NxcGNwdHZTTHRIeHR6YU5VdHJQR2lXT0ExQ2VrVjhRc1ZiUWQ5SDAxU0c0QWRtMTk3WlpJeUdraDVNKzNwWG02SnRsQTZ3MTVJQUJsbW8rM3ZuTFJib3pYRE8yM3ZPeUkvVXpBb3d2M1F4bWdmVE51MS9kaHJuVUlod0xMTDFZQ2UrajBQSWpoTEJsUzJkcXRVTjdRdXZ0dXpCL0ZvTHNkMUNlUVBRVlUwL05xL2JFakR1Qy9aMjFnM0IwdWs4UnRPNk1UY1NWZXRUcHc4ajBRKzg3clNFVTMvUVhPekxrWmdpVDNuVVpTTEVnbkNRTVVmMmV6Rm1QZWpMNytOQWJFQlVTUUVNMVdIN3NKckxKRThQK0tVVW1IQkliQ2VQM2RGT2dNaDBHRUtXWjBhenZzYXlCRjNWNXU4azFFS2VEbGFaTys2Q3dqbHY2OXZva1p2bDQxOCtiVUt5c1JIYjIwYW9QWmJ1WHBnekhoUERkTGRDaXpMTWwiLCJtYWMiOiIwYmZmNzMyZDVkNjk2ZjczNDdmZjIwYjIwM2U4MjI3NTkxYzc5MzgyYzIwODI4YWEzZGVkYTU2ZDg2YzVkNjAwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:11:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImR1MEVQK1FjUVRxYkhLVDZBRXpkZ0E9PSIsInZhbHVlIjoiVTZ6bEhDU0ZEbEk2YnlzbnNtekhXZ1pxbXpzcG03M2FSRytNSXg5SFNOWHA0RE9NWFdLL01QRmdRS3RHK281K1FqdFBJMzBTeEJTaGVQWVk0OTFJQmtMWTdGN0VrWHFvZWZqWnZqdWRiQWF2Zk5zK3UyTCtRL3NVRzVqaTI5TXhtVHVwWWJQZWxrZkR0NHZLdjBnb3JhSW85WVR2bXZVMElLV0grc1NiMVV5bjZUUkFJNTF6YTluVVV6VWFNbWREWE5MV3Q2STVPRkJYY1RKU3JjK2xmT3JndUsrSjBMT0lQUE92S01xK1RnVkcxVE9sR292T1ZtRlV5NkRoMDdBMVJXbkcycTU3WkZBQyszZmswZG9iaHBVUW94eFFqWmRNd3QwUnVDUzdUZW9LN1paeE8zR2NoRnZ6NW1XcjBzV0ZRRGZrWFRzc25QeldCZFZWQVp6dzZXUk5aZ2hWeUVGbWNJVDREME1uZzJKSDgwQTZ3UzNQMVF1NytvWnc0LzFlWm9rNXcxU0w0SUpOMStOYytVN2Qyd3FTYktHZHhKUUJzTmVlbmc2UFVZdDMxUHVGYzhsZGtIMUo3SUt4bFdFZExLZHE5eUg4ck8zeURqTlNuTWZqdGNrc0VFcWFQK0NYdUl2ZzNnT29NNEhJQ25NR0R0OUVHV1FJSmZ1UFcvMkoiLCJtYWMiOiJiNjAwMGIzMTNmNDkyOWJjZDVjMzRlNThlZGM4OTRlOGM3ZTFhZjY3YWZkYjBmZTM0OTMzMGYwYjE4N2YyYTgxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:11:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldYMkJJS0NSY01jUk5vQWljaUVqY2c9PSIsInZhbHVlIjoiTVNiYjVYNkZtdnJ3aWdiTFR4aWNmazFQeGZRTzlGM1Q2TnRCZ3Z0K0ptK1BkZmFvY2N2OVlPZWxMSmYyaHpEUlk4UGJrazRmSWZiWXdJNU5nSnRXckV1K3hWTlg2M0I4cE1oYVVuTVp2OTBVYi8vSWQ3T0U1SjE3MDZCQVZONlRBemMzYmlPc0NxcGNwdHZTTHRIeHR6YU5VdHJQR2lXT0ExQ2VrVjhRc1ZiUWQ5SDAxU0c0QWRtMTk3WlpJeUdraDVNKzNwWG02SnRsQTZ3MTVJQUJsbW8rM3ZuTFJib3pYRE8yM3ZPeUkvVXpBb3d2M1F4bWdmVE51MS9kaHJuVUlod0xMTDFZQ2UrajBQSWpoTEJsUzJkcXRVTjdRdXZ0dXpCL0ZvTHNkMUNlUVBRVlUwL05xL2JFakR1Qy9aMjFnM0IwdWs4UnRPNk1UY1NWZXRUcHc4ajBRKzg3clNFVTMvUVhPekxrWmdpVDNuVVpTTEVnbkNRTVVmMmV6Rm1QZWpMNytOQWJFQlVTUUVNMVdIN3NKckxKRThQK0tVVW1IQkliQ2VQM2RGT2dNaDBHRUtXWjBhenZzYXlCRjNWNXU4azFFS2VEbGFaTys2Q3dqbHY2OXZva1p2bDQxOCtiVUt5c1JIYjIwYW9QWmJ1WHBnekhoUERkTGRDaXpMTWwiLCJtYWMiOiIwYmZmNzMyZDVkNjk2ZjczNDdmZjIwYjIwM2U4MjI3NTkxYzc5MzgyYzIwODI4YWEzZGVkYTU2ZDg2YzVkNjAwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:11:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImR1MEVQK1FjUVRxYkhLVDZBRXpkZ0E9PSIsInZhbHVlIjoiVTZ6bEhDU0ZEbEk2YnlzbnNtekhXZ1pxbXpzcG03M2FSRytNSXg5SFNOWHA0RE9NWFdLL01QRmdRS3RHK281K1FqdFBJMzBTeEJTaGVQWVk0OTFJQmtMWTdGN0VrWHFvZWZqWnZqdWRiQWF2Zk5zK3UyTCtRL3NVRzVqaTI5TXhtVHVwWWJQZWxrZkR0NHZLdjBnb3JhSW85WVR2bXZVMElLV0grc1NiMVV5bjZUUkFJNTF6YTluVVV6VWFNbWREWE5MV3Q2STVPRkJYY1RKU3JjK2xmT3JndUsrSjBMT0lQUE92S01xK1RnVkcxVE9sR292T1ZtRlV5NkRoMDdBMVJXbkcycTU3WkZBQyszZmswZG9iaHBVUW94eFFqWmRNd3QwUnVDUzdUZW9LN1paeE8zR2NoRnZ6NW1XcjBzV0ZRRGZrWFRzc25QeldCZFZWQVp6dzZXUk5aZ2hWeUVGbWNJVDREME1uZzJKSDgwQTZ3UzNQMVF1NytvWnc0LzFlWm9rNXcxU0w0SUpOMStOYytVN2Qyd3FTYktHZHhKUUJzTmVlbmc2UFVZdDMxUHVGYzhsZGtIMUo3SUt4bFdFZExLZHE5eUg4ck8zeURqTlNuTWZqdGNrc0VFcWFQK0NYdUl2ZzNnT29NNEhJQ25NR0R0OUVHV1FJSmZ1UFcvMkoiLCJtYWMiOiJiNjAwMGIzMTNmNDkyOWJjZDVjMzRlNThlZGM4OTRlOGM3ZTFhZjY3YWZkYjBmZTM0OTMzMGYwYjE4N2YyYTgxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:11:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769011107\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1955152107 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955152107\", {\"maxDepth\":0})</script>\n"}}