{"__meta": {"id": "X5291b35e751d95d67d440b982514da4f", "datetime": "2025-07-30 09:32:28", "utime": **********.827605, "method": "GET", "uri": "/finance/sales/products/search?search=sh", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.171175, "end": **********.827627, "duration": 0.656451940536499, "duration_str": "656ms", "measures": [{"label": "Booting", "start": **********.171175, "relative_start": 0, "end": **********.736696, "relative_end": **********.736696, "duration": 0.5655210018157959, "duration_str": "566ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.736708, "relative_start": 0.565532922744751, "end": **********.827629, "relative_end": 2.1457672119140625e-06, "duration": 0.09092116355895996, "duration_str": "90.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46676680, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/products/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchProducts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-946</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01416, "accumulated_duration_str": "14.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.788862, "duration": 0.01289, "duration_str": "12.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 91.031}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.812376, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 91.031, "width_percent": 4.873}, {"sql": "select `id`, `name`, `sale_price`, `sku`, `type` from `product_services` where `created_by` = 79 and (`name` LIKE '%sh%' or `sku` LIKE '%sh%') order by `name` asc limit 50", "type": "query", "params": [], "bindings": ["79", "%sh%", "%sh%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 923}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.816936, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:923", "source": "app/Http/Controllers/FinanceController.php:923", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=923", "ajax": false, "filename": "FinanceController.php", "line": "923"}, "connection": "radhe_same", "start_percent": 95.904, "width_percent": 4.096}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/products/search", "status_code": "<pre class=sf-dump id=sf-dump-2037717111 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2037717111\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-940966419 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">sh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940966419\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-804885699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-804885699\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-702808916 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InA3OHl3YUdYeDVUd0hvT3lyRkoxa0E9PSIsInZhbHVlIjoidkw0T01mYThYa2l4TTY4eUcwWDJMNUt1T2tNNHh0eVVOcnB3SkIrRUZvMFBPak0wNktlOXVYQmFmZWNwbjlTVUNBSjNqTmU5YVZ3SVcvbjZMdlNvMGlFN0wySm51aVJnd1JWQjhxa3Q3b1VzM21ycXpCM3ZkQ2FaVE1xVmRENmVZOEJ6azlxcVY0RVJVeW5ETXFyZzZ5VlkvWFdVQVd2OEJtalE4aThuc1lsNXI5cVRSWkQzNnNucVpWTThrdy9oUiszTXhUdWhPckM5NUwrYnJJWlhBMGVHdzIvMHcrRFdNczY1azVMOXNBVS9Gc0FIU0x2SDJxRmRTcDhFZ0RDNUhaVFA0cVUzcE9SeDdjVnpLMDFhekhxWUNQbzZtS3FsWEFPK3pBVXY5L3dVUDFRZWZiZUVZVmxOMG85WlAyMy9vTHBsYkRjRWY3aTRBblFGR2VoNDdNYU9yTUE2SlA2eHFnVDNma2tVRUV1NlR1dERMR2FSQWljRFh6RnBzbDY0bXNXdnJHcHBqRW9Bbjk4cnB5TUdSUUlXeWZuVmJLVDE3dzFyYTBXekgyaGJvU1FNOXZyM1cwVmhQYkpjZEs4SFV0NXYxZFhVc1JhRURBUkwwNU9oRUVxUUthOG9DNXBwQkNabFpiUTgrODlPckhLVm0vODBSQ20wQUxxMmM2RzciLCJtYWMiOiJhMDVlYWYwZGRhNjJkYzI4MDEzMDUwNTRjNzg0MTkxYWNjMThkNDFlYzExNGFkY2ZhYzY4M2JjNjNmMWYzMDY1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkVRTHNMaENneWdVQ1FXRDMzKzNodWc9PSIsInZhbHVlIjoibWt5dlZzWFJCYWF6UEFrUEF4MDJjcDNXT0tMWjB6NTRsVjJnamcrdXJRenRONEpMWjJpQ3gzMk1tVVFLbjlnU1VVS3phTlBSekFsb21PbEVaS2ZkYlZSRFJLVmh5bFNhV2xhTkdPd3JlaWUzWEQ1cmhZQnAwZjBXajRsVTd0N1h6K1UrWGlVR3VxREdDWk9JNklMS2hrM0NCYitMMGJQbUZtbldTOTNYOWRORURMaUtvSkhDQWFSdXFOYjZDOE1BZGZwdSthZzcxU1BsSFBsemhyWjRJbng2T3VhZ0lWZjRZVDRENTQ4U0ZRMEN6bDllcllSd0w5NDV5QWZXQjVGU0I2MnR4WWl6Lzhhd2s0UzNpbXMyL0pIWkF0b1hkcTR3NjRFbUlWaGJlcitKd2x1d0ZwVU5NMTdQSjkxc29nQWw3eWl1eWFiUkVWZDZWWkl6YXYzK1JqVGkzOHhZV3hsOEhiSnRhcmVBTFpxNUxNQXdsclFVa05LODh0U3JtZmc2c3lCRWxDSERtSjJ4SDZiZFc5Nyt5VE5CdTNyQWFaeEhHZ0lWMk9OQ1ZRaElrajd0Y0dUUHkybFRnMUNzRmw0MmRsZW9lZEJpM3BiMnFrQ3gxaEpkQ3BVR01qOTE4SldGYUl6cHJMQkc4Sit6L3NZVnFGVEs2aCtRWXA4S0VpcU0iLCJtYWMiOiIzZmI4MGRhNmI0MDA1N2IyYTkzZmJiNzgzNDZlNzMxZDM4OTdmYmNlNDU1NzVhOTM5ZTZmMmI3NjQ4ZmMzNGI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702808916\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-156036819 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156036819\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1564949641 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:32:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNRVDgyQitCN3NLZFY2dGJtOU5DdEE9PSIsInZhbHVlIjoiWmRRR294ZnpwaWFWeUZhb3V5M3pZTXJKTUtrejdLT21nVnd3ditrN1FnM2xXbDFYM2UwckZyTG0xM0JFbXFSY1pkd2FhRlZRN00yRUNoWS9lTlltMWcyMFVkZkdhQUFWRlJNR3Zob2p6UzkxNTVGaHVYQi9jQ0pwWmxJSW5qb1lJNXRDR1BVaVFLRnArTkNrQ0FneFU4YlhqV0RwNGJvbU9kbktWOU9vVkFGdHMvblFCL0h0UjlEeTdyNklON2VQcEd4bDdUZzY3VkJzTmlpOWZrdzlIZWVSajhnUVRHemlNNXpyT1Y2dWpMVVJOaUVYcFRPVmYwV3pIZ2VoTERmUEM2b3pJMEtTWElsVG5taVVPQkJZU3VhemlEUUNQZGlQU3RPQVpQN0l4UUh0K0pDR0ZxemU1c2dKZVF2TTBGOU11WWUyNC8wSWcxdFZHWmtCWldxNWtoQmdEKy9jRWpsNUhNMGZTV0VIMTVEZlFrTjhmeUYxV3ZvbXNTcThWekY2cFNYMkYxWW1OUHdBOTlpRWtoNDJ5SXRjMUV6Mm15KzJNSW54QW5INGVVRVVqQ2d3RnpibEkwZGxHNWp2cnA1K3plWFlaMmxzL2YwT1M4RG9tdFp6Y1piZ1pFNUhQZVN4c0NBRTJZNTVWTDhjblJVTFppQmpaNnpDUXpReEQvbkUiLCJtYWMiOiI0MmYwMGQwYWJjZTkwOTI0ZjBlMWVhMzNhODM0MmJlYjUxOWYwMmRjYmY3NmQ0NzhiYWQ0MWZmNzc5ZWMwZjllIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:32:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjdvK0lxZytKUzV3SXJMK0ZLTmpJOVE9PSIsInZhbHVlIjoiU2NhRkFLcFhBNzc2bzNyZXMzcXZHRnZSTkQ4WVdyVGM2LzRyZEFIeGhNL2YrQkJTWFk3VHZRVWxWdXVNaUp5TVh2U1paZVkrbTI2ZFVVM1lXcXZXWXFWUUlaVXNtbFdFWGJIUzhrcENPZmwzTUNDdHA5dGlKelZVWXY2NGowc0VuUmJqcDJqdkswV3FPQzM0eFZKazVKY3pGQ0hWQzd5NHRIUVZOanpSeHpEV2tCOVp0NEhiRkx2ZmFFU2ZCMW1wdS8zSWhUVFpvdDFQN3JJdG5Memc3NHhGRDRKWE5GTHNkOGtFVTBWYkFSaUhXZGZRZkJ5R05rTjZwV1p3cTRnL2oyaVdQam9yNUYzWXRpbmhnbDZPSTMzeTA0VHJ5ZU9HQ3dJbkRZU0NYa3dMMytNV2tsU0dWS2lkQ3hCMFJSdUsrQUNkek5EWkpWQmV3K0k2QzY4dVZyRkhwSm1WYndLUjcvVXlxaTFWRWQ0VzREVlM0NVJ4aE1SSFRUeUs1Mm5jYVZQK0VhNUxRZHp3dS9KT203dGNYbGwxc1BsYytiVlhFakxNL0hIQ2M5a2xFd3lXYUhsejZNZlUzVENRVnBxUER3VWp6QXNPM1RsSUxUY3JyK2tsWnRUTERCZ09nczNmSGFSQ3YyamlkN2pXSVI0VDdTZ3R5ZGJKUnNRYnBOUFciLCJtYWMiOiI0NzlhZmVkYjYzNThkOTVhZjA3N2U1ODI3YzYzZTAwZjEyN2NmNjMyZTZiMmZkYmQwY2M3N2VhNjE4MjczMGNmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:32:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNRVDgyQitCN3NLZFY2dGJtOU5DdEE9PSIsInZhbHVlIjoiWmRRR294ZnpwaWFWeUZhb3V5M3pZTXJKTUtrejdLT21nVnd3ditrN1FnM2xXbDFYM2UwckZyTG0xM0JFbXFSY1pkd2FhRlZRN00yRUNoWS9lTlltMWcyMFVkZkdhQUFWRlJNR3Zob2p6UzkxNTVGaHVYQi9jQ0pwWmxJSW5qb1lJNXRDR1BVaVFLRnArTkNrQ0FneFU4YlhqV0RwNGJvbU9kbktWOU9vVkFGdHMvblFCL0h0UjlEeTdyNklON2VQcEd4bDdUZzY3VkJzTmlpOWZrdzlIZWVSajhnUVRHemlNNXpyT1Y2dWpMVVJOaUVYcFRPVmYwV3pIZ2VoTERmUEM2b3pJMEtTWElsVG5taVVPQkJZU3VhemlEUUNQZGlQU3RPQVpQN0l4UUh0K0pDR0ZxemU1c2dKZVF2TTBGOU11WWUyNC8wSWcxdFZHWmtCWldxNWtoQmdEKy9jRWpsNUhNMGZTV0VIMTVEZlFrTjhmeUYxV3ZvbXNTcThWekY2cFNYMkYxWW1OUHdBOTlpRWtoNDJ5SXRjMUV6Mm15KzJNSW54QW5INGVVRVVqQ2d3RnpibEkwZGxHNWp2cnA1K3plWFlaMmxzL2YwT1M4RG9tdFp6Y1piZ1pFNUhQZVN4c0NBRTJZNTVWTDhjblJVTFppQmpaNnpDUXpReEQvbkUiLCJtYWMiOiI0MmYwMGQwYWJjZTkwOTI0ZjBlMWVhMzNhODM0MmJlYjUxOWYwMmRjYmY3NmQ0NzhiYWQ0MWZmNzc5ZWMwZjllIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:32:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjdvK0lxZytKUzV3SXJMK0ZLTmpJOVE9PSIsInZhbHVlIjoiU2NhRkFLcFhBNzc2bzNyZXMzcXZHRnZSTkQ4WVdyVGM2LzRyZEFIeGhNL2YrQkJTWFk3VHZRVWxWdXVNaUp5TVh2U1paZVkrbTI2ZFVVM1lXcXZXWXFWUUlaVXNtbFdFWGJIUzhrcENPZmwzTUNDdHA5dGlKelZVWXY2NGowc0VuUmJqcDJqdkswV3FPQzM0eFZKazVKY3pGQ0hWQzd5NHRIUVZOanpSeHpEV2tCOVp0NEhiRkx2ZmFFU2ZCMW1wdS8zSWhUVFpvdDFQN3JJdG5Memc3NHhGRDRKWE5GTHNkOGtFVTBWYkFSaUhXZGZRZkJ5R05rTjZwV1p3cTRnL2oyaVdQam9yNUYzWXRpbmhnbDZPSTMzeTA0VHJ5ZU9HQ3dJbkRZU0NYa3dMMytNV2tsU0dWS2lkQ3hCMFJSdUsrQUNkek5EWkpWQmV3K0k2QzY4dVZyRkhwSm1WYndLUjcvVXlxaTFWRWQ0VzREVlM0NVJ4aE1SSFRUeUs1Mm5jYVZQK0VhNUxRZHp3dS9KT203dGNYbGwxc1BsYytiVlhFakxNL0hIQ2M5a2xFd3lXYUhsejZNZlUzVENRVnBxUER3VWp6QXNPM1RsSUxUY3JyK2tsWnRUTERCZ09nczNmSGFSQ3YyamlkN2pXSVI0VDdTZ3R5ZGJKUnNRYnBOUFciLCJtYWMiOiI0NzlhZmVkYjYzNThkOTVhZjA3N2U1ODI3YzYzZTAwZjEyN2NmNjMyZTZiMmZkYmQwY2M3N2VhNjE4MjczMGNmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:32:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1564949641\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1363624103 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363624103\", {\"maxDepth\":0})</script>\n"}}