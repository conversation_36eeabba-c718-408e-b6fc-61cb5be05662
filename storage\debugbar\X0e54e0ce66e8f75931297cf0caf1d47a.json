{"__meta": {"id": "X0e54e0ce66e8f75931297cf0caf1d47a", "datetime": "2025-07-30 09:09:19", "utime": **********.591683, "method": "GET", "uri": "/finance/sales/contacts/lead/12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753866558.78151, "end": **********.591706, "duration": 0.8101959228515625, "duration_str": "810ms", "measures": [{"label": "Booting", "start": 1753866558.78151, "relative_start": 0, "end": **********.466345, "relative_end": **********.466345, "duration": 0.6848349571228027, "duration_str": "685ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.466361, "relative_start": 0.6848509311676025, "end": **********.591709, "relative_end": 2.86102294921875e-06, "duration": 0.12534785270690918, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46686760, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=977\" onclick=\"\">app/Http/Controllers/FinanceController.php:977-1036</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01828, "accumulated_duration_str": "18.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.539984, "duration": 0.01604, "duration_str": "16.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 87.746}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5723832, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 87.746, "width_percent": 6.346}, {"sql": "select * from `leads` where `id` = '12' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["12", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5780349, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1003", "source": "app/Http/Controllers/FinanceController.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1003", "ajax": false, "filename": "FinanceController.php", "line": "1003"}, "connection": "radhe_same", "start_percent": 94.092, "width_percent": 5.908}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/12", "status_code": "<pre class=sf-dump id=sf-dump-2103960499 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2103960499\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1287066003 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1287066003\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-259978675 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-259978675\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-296968855 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlBvdnpwc0JoRnFrOE5ock5naXVySVE9PSIsInZhbHVlIjoiNDlTNm9VeUd3Qjl4a1U5VnlXdnlXVUdZK3oyeEMwZmpQMEZiclpNQ1U0cHhmSzFnYWhNamhiVjgzQUQ4cG5VeWVOQVdEMnJBR2VSVnovZDB1TnhkeDZZTWlpUWUxMlJEeFVzSGwydkNYclQ4M2gyNGtsK0VhRlZ1NzROc2FnK3FzVFhWVnpiNzMvSmNmOEFpTWpLaHVoR3VsSmtWVnVVS0ptUHNqbklFS1UwZWhQVERDNHkxcTNyS0Z1cS9ZWWw5S05GdnoxMkYrc0xHWldqczdWRGtIcXI3RUZQM3JWRmx3ck93RndsNDRIZitRR0w5Wnc1Tnp4RnBaZlUzSW1aZjZmUGEvd0drNFNmRnN0UEdsYmt1VHNQZ05qbjhtZkRvK1ltYldMRzZjbWc3d2tzN0dEYUtENjFXRFlCa3Q3QytudDAvSVREUEN6N0hRTVFQMjc2ZTZoN3Q3ZFZwWmdlWTBpRzQrY09rNkRVdGlMZUZLODRLcWs0aU82cjBEZ050TEkvQXZjQ2IxODltYmg3c21MRGFtazA4bkkxN01CeEQ4UHdCYmZHTDVLTFBWNDhUTUJiZ2xkNmtNRjhQZFRueGtiUit6T1RyL014UEhuV3Y2d2xSQkV6UXp1bGRhSk9kQzBHdXJVNzVwMUFmbGVyV3ZDU0dWNUFGSWNJQU8vL3MiLCJtYWMiOiI3NTI4MmQ0MzNmMDEzNDZkYWEwM2FjMGRjYjc3MGFlYWM0MDQ3ZjE1NTdlZGRlMWVmZTllYWI0ZDE1ODFkYTFlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjQwcUk2NFRVeGRQZXZ5cU5wRGRxV0E9PSIsInZhbHVlIjoiaHJHaUFTcmRDREN2QnZhNzY2Z3dxZTBMSVVST1YycWYvV1JFTVhwVEM2N0F4cC9ETVVIRVdnL0lrMUF0YU5VdE1lenB1RjBHemtJeSswczlyVnRwMDdIVlJVQjhRZDZyeStkb1JXUHNJY1pTaVNZc1VFYWtiMys3eTNWaXhBRmxsMUJwZW1lMGNvUGRpYVhPc1YzTHpIRjRyTkJvYjFPaGFhQ0t5N1lFbmd1RjFyejFnUjNNSE1DeTY1OVg3eUxucGFLejNsaGY1RXU5aWwrQTdueVlmR2RMZHBJMElwTzZnS2F4NjNHQmJGaXRVOTkzNkpDTXZLcDFlWEJvcEE0WGE0OERGM2tQWXcxc1MyY3kxUld2ajlwRCtrbDJuNDFwVHMvWDhLS0oyQzU5MFF4bjZIZk91YzQycGpOV2Yya0M4N3Qxeit3clNXc1lmZUJ2TVp6Rk9vQStyTVdsYTUxWEtDMzNkOUUwZ2h3aXBEaW9iS1JjdFZSWS9UOW9uUW9raVAyY0pwVHVMQlhKRTY1blJuNTIvMklkcHdmYlJlbXgySUNxek1GSWxTNkVKcWZpeGxTcE5qbGNKVnlqOU5Lb2RLdG9hVmE0QlpZOUhnVGsrMTN4QWxHZW9DWWZWNzI1VjNxNGdNZTZwVHM5aXVmeXU4OGtVM1NmWDRIbEZ5M1AiLCJtYWMiOiJmZDFjNWNmZWM3OGFmYWIwOWVjMTcyNGE5MGRlODEwYmUzNjBlZjAxOTY5M2JlYTg3NzkxYjZiYTM4ZDA5ZGQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-296968855\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-156669688 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156669688\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-208120087 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:09:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklHRTdHaWRualdpdzc3M3BpUitPZlE9PSIsInZhbHVlIjoiSEVyTnVoNjI4Yy9RZTZiVU90ZUJFRzBnamFNc0gxU0hNeDlaTVlNNWVXT3A4V2JTMVlrN29qRGt1S0ZIOUs0OFFvUStIMUxhUk9sRmw3RzhDMHFZWUVhU3dZaDFBYWlOZGNobmlvYlgxMUZlSE01UEJ3dzAyLzJmTWtYdWV1M3E5ZThFYk1vL0p0VzFnS1QrNHZ6U1VMRk5OdzhNb0FENkFSd2U5aUM4bFByNkI5WWE5TFhjSzN3bnhaemlGVnNLb2JSVi9RN0ZiWVloa0VkZE8zYmI1ajVaUTVCcm9kN2F1am1ObEJhMWR6WVZoZkxaa1ZldG9nK1NLeU9ncFFQNWdJeExpdTc1eFBoRXpmUFkxYUowQlNhZlh2bjdaVW43WHFxbHN6RE5pL24yWXM4OWgybFh1cFlWajhnVFh3akthNllGUi94V3BqQVNXZDVId0FiWlhyZXlWZElEZkw2N3d2d2hvSVJWaWg4ZFBoYmRPVGVwbXVGU3dlK2dCa2ZRV041TWdMaDB4VjFCbC96Mld5UWQ2U2toaW5VUmRvV3RVYlo4T1F1anpCalg2WnNoVGV1WWxDaDFSbE5pSGZUUEZ2SVd0YTlHQy9sbm1mbjhsUVRUSjZ3d0dLRlpULzdSbGdGYmV1NG5CYXltc2hBWDZjQVBvN0hheDZhbUJVZWYiLCJtYWMiOiIxNTYxOGQ2MmQ0OTJlMmQ2NDUyNmJkMWFmZTRmZDJkZWI5OGNmZWJkOTEyMjlhMzliZjVlNjg3YTA5Y2I1MjQwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:09:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im5LRU15Sk1aNnNWTiswRUhqZHFzK2c9PSIsInZhbHVlIjoiT3BTdnMxYitvc3hray90RUM4T0cyZ1ltd0krODNRUG9oNXU5aEI0SVMxN2JVUm04Vi82M2dYajd4VlM0bDRSSEYyejc1cERSeWlWaUMrWW5UaWkzWFJHSmpFcnNlYVkzUWY3WXl2Y0dvR21XRmVZV2UvSlQzQkE4OHBFM3VaTXFQbDhTVmgra3RUamY2c01acm02T2M2OERXZmcxZXdoMWt6Q0VhWG8rM1VQWkQxQWk3bk5kWFZZU1JwRzVBV25OWXFIRWZ2dTZXeHdSMG1nQSt3RitjWFJZcEpOQU11WHlGQ2ZQOTdONVVLQkhJbi9hZE5XMlFPa1pKUlRPejc3ZFF4RzVkTmRFN2VJNFBjNkZpWlhJcWp4cnBpd050UWhxNFBGeXJPOXFGNFRubEVvQTRLUWhNOGZvUzhCd25sSWRGcXd6THdERWFZWFRKTDFNOGZ1MzRwSTRSUllFSE9vZ0FHNjgzUjd5SE9NRUsrRVlucTh2OER6MEdUUmQvdFRvY1M3WW1wQWttbEdSY3I1TDBFbjV4aXdjTlhkd0oyNkZEUzk2ZU16NVFhNkJOd2NSV2h0b2xxZHZnNTcrcmllVnFieHhUc01GamI5NzUrUGZTbVhBQWJZTlFnQlpUa1NrdUYxblFkYk8weEtPSlB4QVlObHI5RWtKdVF2Y2dsdHIiLCJtYWMiOiJkZGRjNmUxYzBmZTNmMDU2MmRhOWFlMDgyOTMyODBiN2U0OGZlNGIzYTE0MWZiMjg3ZmU2NjQ1Y2RkODI1ZjA5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:09:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklHRTdHaWRualdpdzc3M3BpUitPZlE9PSIsInZhbHVlIjoiSEVyTnVoNjI4Yy9RZTZiVU90ZUJFRzBnamFNc0gxU0hNeDlaTVlNNWVXT3A4V2JTMVlrN29qRGt1S0ZIOUs0OFFvUStIMUxhUk9sRmw3RzhDMHFZWUVhU3dZaDFBYWlOZGNobmlvYlgxMUZlSE01UEJ3dzAyLzJmTWtYdWV1M3E5ZThFYk1vL0p0VzFnS1QrNHZ6U1VMRk5OdzhNb0FENkFSd2U5aUM4bFByNkI5WWE5TFhjSzN3bnhaemlGVnNLb2JSVi9RN0ZiWVloa0VkZE8zYmI1ajVaUTVCcm9kN2F1am1ObEJhMWR6WVZoZkxaa1ZldG9nK1NLeU9ncFFQNWdJeExpdTc1eFBoRXpmUFkxYUowQlNhZlh2bjdaVW43WHFxbHN6RE5pL24yWXM4OWgybFh1cFlWajhnVFh3akthNllGUi94V3BqQVNXZDVId0FiWlhyZXlWZElEZkw2N3d2d2hvSVJWaWg4ZFBoYmRPVGVwbXVGU3dlK2dCa2ZRV041TWdMaDB4VjFCbC96Mld5UWQ2U2toaW5VUmRvV3RVYlo4T1F1anpCalg2WnNoVGV1WWxDaDFSbE5pSGZUUEZ2SVd0YTlHQy9sbm1mbjhsUVRUSjZ3d0dLRlpULzdSbGdGYmV1NG5CYXltc2hBWDZjQVBvN0hheDZhbUJVZWYiLCJtYWMiOiIxNTYxOGQ2MmQ0OTJlMmQ2NDUyNmJkMWFmZTRmZDJkZWI5OGNmZWJkOTEyMjlhMzliZjVlNjg3YTA5Y2I1MjQwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:09:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im5LRU15Sk1aNnNWTiswRUhqZHFzK2c9PSIsInZhbHVlIjoiT3BTdnMxYitvc3hray90RUM4T0cyZ1ltd0krODNRUG9oNXU5aEI0SVMxN2JVUm04Vi82M2dYajd4VlM0bDRSSEYyejc1cERSeWlWaUMrWW5UaWkzWFJHSmpFcnNlYVkzUWY3WXl2Y0dvR21XRmVZV2UvSlQzQkE4OHBFM3VaTXFQbDhTVmgra3RUamY2c01acm02T2M2OERXZmcxZXdoMWt6Q0VhWG8rM1VQWkQxQWk3bk5kWFZZU1JwRzVBV25OWXFIRWZ2dTZXeHdSMG1nQSt3RitjWFJZcEpOQU11WHlGQ2ZQOTdONVVLQkhJbi9hZE5XMlFPa1pKUlRPejc3ZFF4RzVkTmRFN2VJNFBjNkZpWlhJcWp4cnBpd050UWhxNFBGeXJPOXFGNFRubEVvQTRLUWhNOGZvUzhCd25sSWRGcXd6THdERWFZWFRKTDFNOGZ1MzRwSTRSUllFSE9vZ0FHNjgzUjd5SE9NRUsrRVlucTh2OER6MEdUUmQvdFRvY1M3WW1wQWttbEdSY3I1TDBFbjV4aXdjTlhkd0oyNkZEUzk2ZU16NVFhNkJOd2NSV2h0b2xxZHZnNTcrcmllVnFieHhUc01GamI5NzUrUGZTbVhBQWJZTlFnQlpUa1NrdUYxblFkYk8weEtPSlB4QVlObHI5RWtKdVF2Y2dsdHIiLCJtYWMiOiJkZGRjNmUxYzBmZTNmMDU2MmRhOWFlMDgyOTMyODBiN2U0OGZlNGIzYTE0MWZiMjg3ZmU2NjQ1Y2RkODI1ZjA5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:09:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-208120087\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1664443963 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1664443963\", {\"maxDepth\":0})</script>\n"}}