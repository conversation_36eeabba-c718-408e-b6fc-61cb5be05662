{"__meta": {"id": "X7eb23655e30119f181c1d214a88d6176", "datetime": "2025-07-30 09:08:53", "utime": **********.521035, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753866532.899319, "end": **********.521083, "duration": 0.6217641830444336, "duration_str": "622ms", "measures": [{"label": "Booting", "start": 1753866532.899319, "relative_start": 0, "end": **********.456847, "relative_end": **********.456847, "duration": 0.557528018951416, "duration_str": "558ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.45686, "relative_start": 0.****************, "end": **********.521086, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "64.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1860 to 1866\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1860\" onclick=\"\">routes/web.php:1860-1866</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0y7NC7yYN5HSlcWlNj5ZVz4YL9ZVX4P4g8uA7qWI", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1669493111 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1669493111\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1034139841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1034139841\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1036559254 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1036559254\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-467100298 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467100298\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1820208096 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1820208096\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:08:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNQNmpzcStpTm4wemVoNGJXT0JheFE9PSIsInZhbHVlIjoiNTIwZytoMTJKdUxHaXVGek1LTDMrdnBHVG1tVXU0TVI1Q1Q3ZXpNVlNvdWJIT1VuaStCNE93eXVmY09tVmhWbDFoUnQ4TmZYWk0wNmxqQTZEYWRTcGJNcDBSbjJoRi9YWVZMSXFSdVNLRFF4d2JYTmJabGE2MGE1Q1NBdnpNd3BaMmtRT2hneFhBTTBHajBiN0hqVkF5MW91dUJRWElIc1Q5bnF5bWNzSjJtK1EzcHFZUHZQMVFjUnF2bUlxRm0zMTdHcTRwYWJwUzZHcTJJRU5zSVBiaWNBaWFLeng2TTdZN0tPeWtjNmhaVkFLS21oVlhMNmxYN1NLQkg2Z1JRL0NwaGIwOHFKeE1JajJLV2FEWk1WOWJXdDNYUXlERzlQaVBNczZadEVPNHNEd1k1S09XZEZHdGI5TURlWnI1LzVUa1hoNU14U3ZsMTlnUjJMaUpPTnpHaFZaOS9qMm1uMzQ4eE56WWI5QkVsLzEzWkNyaUFPVlU0T0J6VFJnd2VhQk1RZU9OYyt1U2UxdGY4V3B0RVozcENYUTZUQzBoRklqd2p2dUtaTi8veVVOalUrUDlkY3IzLzNGOGRhcnExN21WemFBQnkrVXppSk1qT2F3S0FEaFF2eThOamcrUjkzU2pmd3FYODRXc29YREptem1EajFlNldDWVkyek9xTWUiLCJtYWMiOiI2ODFkZTg0NjJhNDE1Yjg2YzY2M2E0NTdhZTAyY2MyNjJjYzFkNjA3ZmQ4OTJhNTYyNjZjMWI4ODBiMDYwNDcwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:08:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlBKM3dpcFZtRVVYaUcycmZHV0FDOVE9PSIsInZhbHVlIjoiT2hQeVFxWTJNQUFRNTdOK1loR21kaGVKbE83dUVBM1RCVWR1WVkvcVJGVWJWMmM3STZROEVtaUo0K1c1eG1tQ0FxVXRYZDZTYnJuK1M1ME1iLzZ1cFh1ZmhVUUVkd2tNdWhQSnJMQUhYdjYveXJHWkhKdWF1bmxlanR0NEVCNXlwSDd1bVpJeDVEQmQxVG1Mb2g4a1BOZnRzUlFOckJ2WUlwWVFtdTZnVVNUQzBJR3V5TStnZzA5aGlBK01LUlM5RVNaaUVYbmNCaTBmS0VXcEdPMVU5OWRpaWk1N0daemZyNklRWjVpL2xKODlvMktMa2FtaXdwZ1hjWWVNd0d6OUZtY0s2TlltaUlQV25lSFRYSWN0SG9FaU1YQ1dyOVJxblpHWkhZYzh1eWNYNzNrb25VNUZFMXhPenlSMmZaM0JneEU4azBQeTFVd0V0bjJWbmYrNnViZXlmbWVxUGVTcTJRWHh6ZnJtZ0RaRnN3STQ4NTlNT2hSMmRxRGJKeGRDdDhuUGpxdVcyYkdhZDlXSXg1aHJFaVVGY1RQMmp6SXdXYWU1cFRDL01VL01ZMnJRS29xajZPVWxZKzNOSENoY1pzUDlKZEo0RTR4Zkp3c0plNGd3ZmVyVmFma2FwdGphcm5VOFVxZ0l4c3FnZTd6VWhJTVhuaHFibXFpNjlNTkMiLCJtYWMiOiI0NDIyODYxNDFhZTg2YTRjZTNlNmE5NWFlNDI3M2Y3ZjI0MzRhNmUyYzcwODY4MGNiNzgxNDUyYTAyODM2NTRhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:08:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNQNmpzcStpTm4wemVoNGJXT0JheFE9PSIsInZhbHVlIjoiNTIwZytoMTJKdUxHaXVGek1LTDMrdnBHVG1tVXU0TVI1Q1Q3ZXpNVlNvdWJIT1VuaStCNE93eXVmY09tVmhWbDFoUnQ4TmZYWk0wNmxqQTZEYWRTcGJNcDBSbjJoRi9YWVZMSXFSdVNLRFF4d2JYTmJabGE2MGE1Q1NBdnpNd3BaMmtRT2hneFhBTTBHajBiN0hqVkF5MW91dUJRWElIc1Q5bnF5bWNzSjJtK1EzcHFZUHZQMVFjUnF2bUlxRm0zMTdHcTRwYWJwUzZHcTJJRU5zSVBiaWNBaWFLeng2TTdZN0tPeWtjNmhaVkFLS21oVlhMNmxYN1NLQkg2Z1JRL0NwaGIwOHFKeE1JajJLV2FEWk1WOWJXdDNYUXlERzlQaVBNczZadEVPNHNEd1k1S09XZEZHdGI5TURlWnI1LzVUa1hoNU14U3ZsMTlnUjJMaUpPTnpHaFZaOS9qMm1uMzQ4eE56WWI5QkVsLzEzWkNyaUFPVlU0T0J6VFJnd2VhQk1RZU9OYyt1U2UxdGY4V3B0RVozcENYUTZUQzBoRklqd2p2dUtaTi8veVVOalUrUDlkY3IzLzNGOGRhcnExN21WemFBQnkrVXppSk1qT2F3S0FEaFF2eThOamcrUjkzU2pmd3FYODRXc29YREptem1EajFlNldDWVkyek9xTWUiLCJtYWMiOiI2ODFkZTg0NjJhNDE1Yjg2YzY2M2E0NTdhZTAyY2MyNjJjYzFkNjA3ZmQ4OTJhNTYyNjZjMWI4ODBiMDYwNDcwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:08:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlBKM3dpcFZtRVVYaUcycmZHV0FDOVE9PSIsInZhbHVlIjoiT2hQeVFxWTJNQUFRNTdOK1loR21kaGVKbE83dUVBM1RCVWR1WVkvcVJGVWJWMmM3STZROEVtaUo0K1c1eG1tQ0FxVXRYZDZTYnJuK1M1ME1iLzZ1cFh1ZmhVUUVkd2tNdWhQSnJMQUhYdjYveXJHWkhKdWF1bmxlanR0NEVCNXlwSDd1bVpJeDVEQmQxVG1Mb2g4a1BOZnRzUlFOckJ2WUlwWVFtdTZnVVNUQzBJR3V5TStnZzA5aGlBK01LUlM5RVNaaUVYbmNCaTBmS0VXcEdPMVU5OWRpaWk1N0daemZyNklRWjVpL2xKODlvMktMa2FtaXdwZ1hjWWVNd0d6OUZtY0s2TlltaUlQV25lSFRYSWN0SG9FaU1YQ1dyOVJxblpHWkhZYzh1eWNYNzNrb25VNUZFMXhPenlSMmZaM0JneEU4azBQeTFVd0V0bjJWbmYrNnViZXlmbWVxUGVTcTJRWHh6ZnJtZ0RaRnN3STQ4NTlNT2hSMmRxRGJKeGRDdDhuUGpxdVcyYkdhZDlXSXg1aHJFaVVGY1RQMmp6SXdXYWU1cFRDL01VL01ZMnJRS29xajZPVWxZKzNOSENoY1pzUDlKZEo0RTR4Zkp3c0plNGd3ZmVyVmFma2FwdGphcm5VOFVxZ0l4c3FnZTd6VWhJTVhuaHFibXFpNjlNTkMiLCJtYWMiOiI0NDIyODYxNDFhZTg2YTRjZTNlNmE5NWFlNDI3M2Y3ZjI0MzRhNmUyYzcwODY4MGNiNzgxNDUyYTAyODM2NTRhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:08:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1367625188 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0y7NC7yYN5HSlcWlNj5ZVz4YL9ZVX4P4g8uA7qWI</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367625188\", {\"maxDepth\":0})</script>\n"}}