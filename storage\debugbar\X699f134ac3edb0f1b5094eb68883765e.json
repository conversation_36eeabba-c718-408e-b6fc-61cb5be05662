{"__meta": {"id": "X699f134ac3edb0f1b5094eb68883765e", "datetime": "2025-07-30 09:32:34", "utime": **********.707441, "method": "GET", "uri": "/finance/sales/products/search?search=bo", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.082607, "end": **********.707462, "duration": 0.6248550415039062, "duration_str": "625ms", "measures": [{"label": "Booting", "start": **********.082607, "relative_start": 0, "end": **********.622513, "relative_end": **********.622513, "duration": 0.5399060249328613, "duration_str": "540ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.622554, "relative_start": 0.5399470329284668, "end": **********.707464, "relative_end": 1.9073486328125e-06, "duration": 0.08490991592407227, "duration_str": "84.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46676680, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/products/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchProducts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-946</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01001, "accumulated_duration_str": "10.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.671732, "duration": 0.00828, "duration_str": "8.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 82.717}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.690594, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 82.717, "width_percent": 7.493}, {"sql": "select `id`, `name`, `sale_price`, `sku`, `type` from `product_services` where `created_by` = 79 and (`name` LIKE '%bo%' or `sku` LIKE '%bo%') order by `name` asc limit 50", "type": "query", "params": [], "bindings": ["79", "%bo%", "%bo%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 923}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6961582, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:923", "source": "app/Http/Controllers/FinanceController.php:923", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=923", "ajax": false, "filename": "FinanceController.php", "line": "923"}, "connection": "radhe_same", "start_percent": 90.21, "width_percent": 9.79}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/products/search", "status_code": "<pre class=sf-dump id=sf-dump-239147667 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-239147667\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-62826383 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">bo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62826383\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2130673596 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2130673596\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-250087155 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlNRVDgyQitCN3NLZFY2dGJtOU5DdEE9PSIsInZhbHVlIjoiWmRRR294ZnpwaWFWeUZhb3V5M3pZTXJKTUtrejdLT21nVnd3ditrN1FnM2xXbDFYM2UwckZyTG0xM0JFbXFSY1pkd2FhRlZRN00yRUNoWS9lTlltMWcyMFVkZkdhQUFWRlJNR3Zob2p6UzkxNTVGaHVYQi9jQ0pwWmxJSW5qb1lJNXRDR1BVaVFLRnArTkNrQ0FneFU4YlhqV0RwNGJvbU9kbktWOU9vVkFGdHMvblFCL0h0UjlEeTdyNklON2VQcEd4bDdUZzY3VkJzTmlpOWZrdzlIZWVSajhnUVRHemlNNXpyT1Y2dWpMVVJOaUVYcFRPVmYwV3pIZ2VoTERmUEM2b3pJMEtTWElsVG5taVVPQkJZU3VhemlEUUNQZGlQU3RPQVpQN0l4UUh0K0pDR0ZxemU1c2dKZVF2TTBGOU11WWUyNC8wSWcxdFZHWmtCWldxNWtoQmdEKy9jRWpsNUhNMGZTV0VIMTVEZlFrTjhmeUYxV3ZvbXNTcThWekY2cFNYMkYxWW1OUHdBOTlpRWtoNDJ5SXRjMUV6Mm15KzJNSW54QW5INGVVRVVqQ2d3RnpibEkwZGxHNWp2cnA1K3plWFlaMmxzL2YwT1M4RG9tdFp6Y1piZ1pFNUhQZVN4c0NBRTJZNTVWTDhjblJVTFppQmpaNnpDUXpReEQvbkUiLCJtYWMiOiI0MmYwMGQwYWJjZTkwOTI0ZjBlMWVhMzNhODM0MmJlYjUxOWYwMmRjYmY3NmQ0NzhiYWQ0MWZmNzc5ZWMwZjllIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjdvK0lxZytKUzV3SXJMK0ZLTmpJOVE9PSIsInZhbHVlIjoiU2NhRkFLcFhBNzc2bzNyZXMzcXZHRnZSTkQ4WVdyVGM2LzRyZEFIeGhNL2YrQkJTWFk3VHZRVWxWdXVNaUp5TVh2U1paZVkrbTI2ZFVVM1lXcXZXWXFWUUlaVXNtbFdFWGJIUzhrcENPZmwzTUNDdHA5dGlKelZVWXY2NGowc0VuUmJqcDJqdkswV3FPQzM0eFZKazVKY3pGQ0hWQzd5NHRIUVZOanpSeHpEV2tCOVp0NEhiRkx2ZmFFU2ZCMW1wdS8zSWhUVFpvdDFQN3JJdG5Memc3NHhGRDRKWE5GTHNkOGtFVTBWYkFSaUhXZGZRZkJ5R05rTjZwV1p3cTRnL2oyaVdQam9yNUYzWXRpbmhnbDZPSTMzeTA0VHJ5ZU9HQ3dJbkRZU0NYa3dMMytNV2tsU0dWS2lkQ3hCMFJSdUsrQUNkek5EWkpWQmV3K0k2QzY4dVZyRkhwSm1WYndLUjcvVXlxaTFWRWQ0VzREVlM0NVJ4aE1SSFRUeUs1Mm5jYVZQK0VhNUxRZHp3dS9KT203dGNYbGwxc1BsYytiVlhFakxNL0hIQ2M5a2xFd3lXYUhsejZNZlUzVENRVnBxUER3VWp6QXNPM1RsSUxUY3JyK2tsWnRUTERCZ09nczNmSGFSQ3YyamlkN2pXSVI0VDdTZ3R5ZGJKUnNRYnBOUFciLCJtYWMiOiI0NzlhZmVkYjYzNThkOTVhZjA3N2U1ODI3YzYzZTAwZjEyN2NmNjMyZTZiMmZkYmQwY2M3N2VhNjE4MjczMGNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250087155\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-728864535 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728864535\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:32:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJFQXBCcnhMQTNPNTE1RFJhcEpoeGc9PSIsInZhbHVlIjoiZDlIS2xiZXdTbEZ2Szd3VHZrYkVGMGRRUEZ3QmFTdHl6ZjVmLzMwMnBSTTd5dUtQS3hEK1dGcTJ0U3o5alZack5qeVNHdnExOTNxNFVRQXV1NTBVd3NPNzlmdVVQWXJRbExvM05RRE5yb21ZMERCMFZOKzFhSE1SNWFHRURKcFBSY1VkUytFQ1JkcFU4YkFhRFVsZ2ZIMnNLbkM2S01ValZibzRPUWQ2dG83TUpYQkZHRzVaUTY5cllQZmZ3bFQvSjRQeDVjRUFRbTd3Nk5LUjdDcVlFZGZrY3FBZ0FGbjl4bkc1Tk5zdVZPVm1KQVFWVmxnQU1sZS9NNlVWbld3bGk4UmpqTml2L1YyaGlSK21JVzl3Z0M4VjdPd202d1lWbjd5dnNUV1BXUnFSaHRnWSswNjNXNXRpWGRvd3BlNHZoZk02OXhaaWNNZFpsOGRzc1BkZWhORmluYUJOV0Q3ZlQ1K3FaTUIvdjd2UGVLU1BvR0xVVVlHVVY2N2F3ZjdZVTQwaFJGaXBxLzVjWnVKWWFSMHoyR0p3OHN6bzVtUzgwdndvaXdmSGVQR1F6ZUxTZjRMZ3V5R0RrN1dCdllNeWNrL1h6d1FTWTVleEFZUEplK1k2aHVRbnllY2pGNXUvUzc1cmxUVGJGMkpPUC80Q3ZOZXYrL3crbUNhZ1NGWjYiLCJtYWMiOiIxZTAxMDY1N2U2ZGQ2MjE1Mjc0ZmUyMGZhMGMwM2I0NWRkYjJkYzk2ZjZlMmU2ZDI3NzYwZDM0MDkzN2RhOWQxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:32:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlMyc09yZVRCVXNtaXlGZTl5TVFQNnc9PSIsInZhbHVlIjoickIrQlJRYU0rbXNjYWxQc1Erd0JYenNYU1E1UG1jd3JwbXpGa21ZVzJNUWUvblZUUWNxajBhUUlZUEdYUTc0Z1pzMzBZSW1wZGNBNW1xaVUvcHEySTd6S2h0blJJZ2s0QlZQcnJqQ1hvTXU3M09uQlBkQkl6Ukk4cGVCRVhlYTV2WlVFc1BrK0tPUmZLRk1tZVAwV0liWXN0TnZRbnBZMDhYNFEyUDNwVEZBS01STWp1TnhsQk1aY2t6RVQ3KzNuR0VrY2NzKzl2NWpkOUlyTWFwMjcrWXJlRm8xMWg1RitXK2U3Q3ZyUEt1VW5BZkROV1pNaVNnOWZZZ0dHcHU4R2RGemErdTNmNUZFMjFNVWEvdWI1VGc1MkttSkc0aXNCZENKeEV1emZPdFlsWVhSNk85UU56c0duMXFHZzJmUHdZd0JwRXphcDUrTEZiQWZjT2VZUlhTMXZwSm5BejVnMi95RHVMNERnQ2RQQW9ubXNIT1VPbVE1RmNlMEZkVzhxN2QyMnRGMDdjZTJwL3R5MURCRytvRm53YUVtR3Y1Rk81MTZQMkY3eTlvVzMzVXVXdG9rUmlyckVrVnhYR012NjV5MFRTUGplTzVRUVg2MExGQmFmRFNESjNTSDFpeWtieUgxR0s5cDBjMTJqN3ZqRE1FakJSakg1MmtNdHVrT1giLCJtYWMiOiJkMDFlNjIyOWQ3NjY2YWMyZjBlOWVlYTZkZTNhODY0NjJhODY4NDRlNjA1ZWU2ZGNkNmUxMjU4OGVjYjNlZGFmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:32:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJFQXBCcnhMQTNPNTE1RFJhcEpoeGc9PSIsInZhbHVlIjoiZDlIS2xiZXdTbEZ2Szd3VHZrYkVGMGRRUEZ3QmFTdHl6ZjVmLzMwMnBSTTd5dUtQS3hEK1dGcTJ0U3o5alZack5qeVNHdnExOTNxNFVRQXV1NTBVd3NPNzlmdVVQWXJRbExvM05RRE5yb21ZMERCMFZOKzFhSE1SNWFHRURKcFBSY1VkUytFQ1JkcFU4YkFhRFVsZ2ZIMnNLbkM2S01ValZibzRPUWQ2dG83TUpYQkZHRzVaUTY5cllQZmZ3bFQvSjRQeDVjRUFRbTd3Nk5LUjdDcVlFZGZrY3FBZ0FGbjl4bkc1Tk5zdVZPVm1KQVFWVmxnQU1sZS9NNlVWbld3bGk4UmpqTml2L1YyaGlSK21JVzl3Z0M4VjdPd202d1lWbjd5dnNUV1BXUnFSaHRnWSswNjNXNXRpWGRvd3BlNHZoZk02OXhaaWNNZFpsOGRzc1BkZWhORmluYUJOV0Q3ZlQ1K3FaTUIvdjd2UGVLU1BvR0xVVVlHVVY2N2F3ZjdZVTQwaFJGaXBxLzVjWnVKWWFSMHoyR0p3OHN6bzVtUzgwdndvaXdmSGVQR1F6ZUxTZjRMZ3V5R0RrN1dCdllNeWNrL1h6d1FTWTVleEFZUEplK1k2aHVRbnllY2pGNXUvUzc1cmxUVGJGMkpPUC80Q3ZOZXYrL3crbUNhZ1NGWjYiLCJtYWMiOiIxZTAxMDY1N2U2ZGQ2MjE1Mjc0ZmUyMGZhMGMwM2I0NWRkYjJkYzk2ZjZlMmU2ZDI3NzYwZDM0MDkzN2RhOWQxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:32:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlMyc09yZVRCVXNtaXlGZTl5TVFQNnc9PSIsInZhbHVlIjoickIrQlJRYU0rbXNjYWxQc1Erd0JYenNYU1E1UG1jd3JwbXpGa21ZVzJNUWUvblZUUWNxajBhUUlZUEdYUTc0Z1pzMzBZSW1wZGNBNW1xaVUvcHEySTd6S2h0blJJZ2s0QlZQcnJqQ1hvTXU3M09uQlBkQkl6Ukk4cGVCRVhlYTV2WlVFc1BrK0tPUmZLRk1tZVAwV0liWXN0TnZRbnBZMDhYNFEyUDNwVEZBS01STWp1TnhsQk1aY2t6RVQ3KzNuR0VrY2NzKzl2NWpkOUlyTWFwMjcrWXJlRm8xMWg1RitXK2U3Q3ZyUEt1VW5BZkROV1pNaVNnOWZZZ0dHcHU4R2RGemErdTNmNUZFMjFNVWEvdWI1VGc1MkttSkc0aXNCZENKeEV1emZPdFlsWVhSNk85UU56c0duMXFHZzJmUHdZd0JwRXphcDUrTEZiQWZjT2VZUlhTMXZwSm5BejVnMi95RHVMNERnQ2RQQW9ubXNIT1VPbVE1RmNlMEZkVzhxN2QyMnRGMDdjZTJwL3R5MURCRytvRm53YUVtR3Y1Rk81MTZQMkY3eTlvVzMzVXVXdG9rUmlyckVrVnhYR012NjV5MFRTUGplTzVRUVg2MExGQmFmRFNESjNTSDFpeWtieUgxR0s5cDBjMTJqN3ZqRE1FakJSakg1MmtNdHVrT1giLCJtYWMiOiJkMDFlNjIyOWQ3NjY2YWMyZjBlOWVlYTZkZTNhODY0NjJhODY4NDRlNjA1ZWU2ZGNkNmUxMjU4OGVjYjNlZGFmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:32:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}