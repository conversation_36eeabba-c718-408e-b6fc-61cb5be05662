{"__meta": {"id": "X4775517afe6533db546a6910d12d1502", "datetime": "2025-07-30 09:35:34", "utime": **********.616621, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753868133.172454, "end": **********.616649, "duration": 1.4441947937011719, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1753868133.172454, "relative_start": 0, "end": **********.433609, "relative_end": **********.433609, "duration": 1.2611548900604248, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.433626, "relative_start": 1.261171817779541, "end": **********.616652, "relative_end": 3.0994415283203125e-06, "duration": 0.18302607536315918, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46612008, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.549542, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.563942, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.598899, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02958, "accumulated_duration_str": "29.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.493822, "duration": 0.01467, "duration_str": "14.67ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 49.594}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5141332, "duration": 0.0094, "duration_str": "9.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 49.594, "width_percent": 31.778}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.52988, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 81.373, "width_percent": 3.009}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5509999, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 84.381, "width_percent": 3.245}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.565361, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 87.627, "width_percent": 3.414}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.582644, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 91.041, "width_percent": 2.4}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.58781, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 93.442, "width_percent": 3.381}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5943599, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 96.822, "width_percent": 3.178}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8UwjHyctwUeDQCCUrlE8ZiK1A5wLkY7ZRMzttZZK", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1186964083 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1186964083\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1070511169 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1070511169\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-608103989 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-608103989\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1037553028 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037553028\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-344240041 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-344240041\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-911194474 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:35:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZyMGgwZXRiQTJaYlB0aFBQTmJLUUE9PSIsInZhbHVlIjoidUxyNFhKNTF1RjU1UkQ1Mm92N052U2pRc1N6eTB4NDUvMmtSMUpoZTJXeSt3TkZ6VDUvRjk2VWhSWDMzcXJkdVhtODVMRjhta2VNWEgzbXVxVmtCdy8raWRWU2QvK1dlckZKcTNHejJzY0RtRkcyY0l0WG81UGNiaERxQU9IYlV4SEFsZ1hjRXBLMWViejgyeDFFWDIzNjhXZ2poK2V0dHk4OUJGeStKWkUyR21ZbDJNZXM0VjlCazhlOUFvWTdhdFdsdEdyVEJDdFl6dlNIbC9kYlhFS0N3SkVNTXplaEpSRVY1ZzdKTHdjVW83aEVxMGxNN1JlQVR6b09zam81ZThsMDJyM0lRL2NPR0MyZmdiRG1QZFZaODhjSnBYVHR2OXorZmE2d01nUldDQStjWjArKzRKelpMYlpvaEpLVC9tOHNzMklMcHpxNkZCN1Q5N1JzL29RMjN1RitRSnpNUUJ2VWplNy9obE1DUit6ajBSMGVSVEhuUG9RQ1JSZWFpOUVPS3g3SlQxdFJFYTNNWVZFcEgwTWRDRUJLbTZVd3ZHVEF0TlVRSmNVMnJJeVJXM2Y5ZXp3YUlRdnFiaklMZ085aUdWcTViS25XQTZWKzgrWWJnQUtMaDZhUkt0K3hURGJTYmNMMkQ0UWQxbEN2QWNlZDVka0p3VnV6amJDekIiLCJtYWMiOiIyZDI3ZWRkYjI3YWQ5YWI5Njc0ZTU0YjA0ZGZlMTBlMWMzMzcxNDAxZDRkYTk5MTM1ZTUyZjczYjhkMGY4M2ZjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:35:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkN1MEd3V0kzOXJxb3Y2ZVF2MjF4eXc9PSIsInZhbHVlIjoiZzRnSFFrVTdDUmwxTSt0RHhrbzl1eVFxWUtZYzBOSTA0VjN1YUJVSWdMSjJUWnNJb0JRNTB1dWdTeWJ6SGFOMFVSdTBwSHZkLzhsdUc2SVVBWktJTDFiVWRwYnFXQWR3RUJzcnd4TEFqOTh0U1FoS1JIdml2RDJueUEySHRjVzMvN1pLRUxkYUwzWWt4ZWpIK2MxNmNQbzNOQ0pqRVRjQ1UzYlhrb3lCYUtxNUc5NE9ZUHBSK3VFdFNsOGVNVmdDUklnQ25MR0pIQmgzd1lSMDNlS0cxZGxHRktYS3RqTzFxV3gwbm5wS2lCRVpLOWpzc3BuVm9PNTZVeHlkZzNTWUluUXcxV3R4K0xPUFBWMmxCTEpTUmRjOVpGMGI5dDNGL0tnWVpubkh5TFM4UW1PMHZlNmFIb0pScVdXTnFnSTcrSVFnVFZSaUVxbTJNenB6TzNybmhIMml6dlhIR053a3JZYmJCeXVMS0tCMlhSTG4rSDIvWGNwcE5qTzkwMVlVNWU1MUU2a0Jyek05VlM1MEQrcVZGb2YvNFdVdFJMbkx2YVF3ODNDYmxVR1dZNE9jc2JzRFh3Nzhkd2cvNlpQQ3J6Ty94aWg0K2tmUStSRC9PSmxMYnE3UmZxa2JsZVNjM1AxKytTcEZYbnJveXJsZTFiTzlMdEorWE0wOUgyQi8iLCJtYWMiOiI0NjM4NzlmY2JhZjIxNjMxYzc2MmUwZmQ0YmRhMWQ5MmIwM2E3YmU3NjI2MWNhZjMzYTdhNzRjYWI3Nzg2NGZkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:35:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZyMGgwZXRiQTJaYlB0aFBQTmJLUUE9PSIsInZhbHVlIjoidUxyNFhKNTF1RjU1UkQ1Mm92N052U2pRc1N6eTB4NDUvMmtSMUpoZTJXeSt3TkZ6VDUvRjk2VWhSWDMzcXJkdVhtODVMRjhta2VNWEgzbXVxVmtCdy8raWRWU2QvK1dlckZKcTNHejJzY0RtRkcyY0l0WG81UGNiaERxQU9IYlV4SEFsZ1hjRXBLMWViejgyeDFFWDIzNjhXZ2poK2V0dHk4OUJGeStKWkUyR21ZbDJNZXM0VjlCazhlOUFvWTdhdFdsdEdyVEJDdFl6dlNIbC9kYlhFS0N3SkVNTXplaEpSRVY1ZzdKTHdjVW83aEVxMGxNN1JlQVR6b09zam81ZThsMDJyM0lRL2NPR0MyZmdiRG1QZFZaODhjSnBYVHR2OXorZmE2d01nUldDQStjWjArKzRKelpMYlpvaEpLVC9tOHNzMklMcHpxNkZCN1Q5N1JzL29RMjN1RitRSnpNUUJ2VWplNy9obE1DUit6ajBSMGVSVEhuUG9RQ1JSZWFpOUVPS3g3SlQxdFJFYTNNWVZFcEgwTWRDRUJLbTZVd3ZHVEF0TlVRSmNVMnJJeVJXM2Y5ZXp3YUlRdnFiaklMZ085aUdWcTViS25XQTZWKzgrWWJnQUtMaDZhUkt0K3hURGJTYmNMMkQ0UWQxbEN2QWNlZDVka0p3VnV6amJDekIiLCJtYWMiOiIyZDI3ZWRkYjI3YWQ5YWI5Njc0ZTU0YjA0ZGZlMTBlMWMzMzcxNDAxZDRkYTk5MTM1ZTUyZjczYjhkMGY4M2ZjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:35:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkN1MEd3V0kzOXJxb3Y2ZVF2MjF4eXc9PSIsInZhbHVlIjoiZzRnSFFrVTdDUmwxTSt0RHhrbzl1eVFxWUtZYzBOSTA0VjN1YUJVSWdMSjJUWnNJb0JRNTB1dWdTeWJ6SGFOMFVSdTBwSHZkLzhsdUc2SVVBWktJTDFiVWRwYnFXQWR3RUJzcnd4TEFqOTh0U1FoS1JIdml2RDJueUEySHRjVzMvN1pLRUxkYUwzWWt4ZWpIK2MxNmNQbzNOQ0pqRVRjQ1UzYlhrb3lCYUtxNUc5NE9ZUHBSK3VFdFNsOGVNVmdDUklnQ25MR0pIQmgzd1lSMDNlS0cxZGxHRktYS3RqTzFxV3gwbm5wS2lCRVpLOWpzc3BuVm9PNTZVeHlkZzNTWUluUXcxV3R4K0xPUFBWMmxCTEpTUmRjOVpGMGI5dDNGL0tnWVpubkh5TFM4UW1PMHZlNmFIb0pScVdXTnFnSTcrSVFnVFZSaUVxbTJNenB6TzNybmhIMml6dlhIR053a3JZYmJCeXVMS0tCMlhSTG4rSDIvWGNwcE5qTzkwMVlVNWU1MUU2a0Jyek05VlM1MEQrcVZGb2YvNFdVdFJMbkx2YVF3ODNDYmxVR1dZNE9jc2JzRFh3Nzhkd2cvNlpQQ3J6Ty94aWg0K2tmUStSRC9PSmxMYnE3UmZxa2JsZVNjM1AxKytTcEZYbnJveXJsZTFiTzlMdEorWE0wOUgyQi8iLCJtYWMiOiI0NjM4NzlmY2JhZjIxNjMxYzc2MmUwZmQ0YmRhMWQ5MmIwM2E3YmU3NjI2MWNhZjMzYTdhNzRjYWI3Nzg2NGZkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:35:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911194474\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1376626428 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8UwjHyctwUeDQCCUrlE8ZiK1A5wLkY7ZRMzttZZK</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376626428\", {\"maxDepth\":0})</script>\n"}}