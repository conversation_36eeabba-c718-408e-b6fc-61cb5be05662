{"__meta": {"id": "Xb9fa89b83e766464ec1a2c47ea4cb3c0", "datetime": "2025-07-30 09:30:06", "utime": **********.333899, "method": "GET", "uri": "/finance/sales/products/search?search=Bot", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753867805.658783, "end": **********.333921, "duration": 0.675137996673584, "duration_str": "675ms", "measures": [{"label": "Booting", "start": 1753867805.658783, "relative_start": 0, "end": **********.229111, "relative_end": **********.229111, "duration": 0.5703279972076416, "duration_str": "570ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.229127, "relative_start": 0.5703439712524414, "end": **********.333923, "relative_end": 2.1457672119140625e-06, "duration": 0.10479617118835449, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46676680, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/products/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchProducts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-946</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00771, "accumulated_duration_str": "7.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.292886, "duration": 0.00614, "duration_str": "6.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 79.637}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3147638, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 79.637, "width_percent": 10.117}, {"sql": "select `id`, `name`, `sale_price`, `sku`, `type` from `product_services` where `created_by` = 79 and (`name` LIKE '%Bot%' or `sku` LIKE '%Bot%') order by `name` asc limit 50", "type": "query", "params": [], "bindings": ["79", "%Bot%", "%Bot%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 923}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3218849, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:923", "source": "app/Http/Controllers/FinanceController.php:923", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=923", "ajax": false, "filename": "FinanceController.php", "line": "923"}, "connection": "radhe_same", "start_percent": 89.754, "width_percent": 10.246}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/products/search", "status_code": "<pre class=sf-dump id=sf-dump-1583410925 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1583410925\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1056156243 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Bot</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1056156243\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-674555532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-674555532\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-939896312 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjRHc3JLYWxPazljNENMVUZSZzUwRWc9PSIsInZhbHVlIjoiWTMrSWRxTmZ4czd5MUZiN005TndMa2VjTFhzMkNqa3ZhWCtSYnRBV0RjZ3dRa1d4WXNBR09NY2lTbDJtQVBIdWdqWWVPbDhPL1BuVnlkR0VKdUZtVWZjelljN09UVDBzcE16aU52NERFZzZrd1kvR2hpSFRYak53elp1Ny8rUnc3c05rT3ZUazJXVDJJTzZnaThWa2p2Rld3YmNsdkFqVjBFcGFic0lNRDREZkNMNkk4MDBNZUoyQXlZWmc4Zml5LzFLNmlibmhHTUNVMDh4dGhHdkNFcjVrS3p1SU1nVzFtWnBPS09LQVBjZklnU2dOK0hwSW5tcFptSUdaWnhySFc3WnR5RVBJSzIyY1Nwak54VFI1TEN3Ym1uV3BldWhLYWdOMUlCWmFKSmdXTS83UkQ1M1hGQjJaanA3NVFxa1llTFlHdDRmb3FjanFpR3NHRFpXazYyS0N1Z0k2a0VjMHVaU3NHdWFHRGNDVUNlbmdYN0FhNHF1L3FMYzZub3VjZGgyd3JtQU5YbWpGVTByaSttMmxOYTBCbVpVbTdKQVhVcDYwNkZ4Qm9UL2dzSWJ3M0dUY2o3eGp1bTB3NVVRY0hocTBJSnRxaVI5QnBKek9SdGYxZUxxRUxweUJwdXNHZ3EveUhGb2tYa0xYKzdmV1BIYndhM3M0akNvM2ZWRmoiLCJtYWMiOiI5YTNkZmI1NmJmNjEzY2Q3MjBmOTNjNWU2YjE3YWFlMTFiNjk5MGRmZTcxOTY4MTQ4Zjg3MjU5NmRlODg5MzFhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InpYNWNlVkZqRGl6eDVFQnBKODNvWmc9PSIsInZhbHVlIjoieTFzamhKdHBFNmZ4YzBicGVzT0N1NjEyWkdSWWdqQTA4UnhRTzB0b2k5RnVQNW41eFdVVDFSVTFZVEJ5c21rK3k1Z3JzVHk1T2lYNE9kV0I3WlJOMzZBc0ZhSktXMFFMR2ZiZU16VXRWYVl3OFJQbHpITk80VjFZQzgvZ1k2ZUFBRDJ5OXhkcFdIZWVXVUdwTnlEOTZzSFJzMVdTN09wNnFvOXp1Q1hyVzUwU25MZW1OWHRGRHhlVVVzSFYxbW9Td293Q25NMFBzMnhwQzhWRlZmSFZTRzNuZE40czRKdVJ5OUVuYnVjZWZIdE50STFoVzNwak9nbjZUMVF3WXp4OG5KNXJscEVjWnRmbXJlWVNjUkNET3dvSWxmL1k0UFNkeXpSWmVGVGtiTzR0UW5KTGljTUN2a003Q25RMVBhL2dZd0s3dC9xc3RZazNsT3FVN2hadDNrWFVFaFA1YnN3WGNhc0lPaGR4Yk1qZm9IUVRtcHByTjYycUd5MkdYU05uSEI2RFVKY0dheSsxWlNXUWIwZWlsQnhCZXJjYUtVN3g0UDY4YzNWalc1c2RJMm1IOFVmUVFncFBFalVpRWxGUFQ2K21tME1GZ0c2M1V2MVRhN3lNQm8vU3p6ZWczNTNsbFdZN2o0WmZidFJFQjU5OEpsV0dsK3lmVlhJbTNqS2wiLCJtYWMiOiJiZGE1OWRmMDA0MDlhMDRkNTEwODc4NTFiOWNjOTU5Yzg1ODU4M2FjYjRhN2ZiOTZhMDJlYTUzNzAzMDZmNmI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-939896312\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-238151428 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-238151428\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-518578669 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:30:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFLdWJoTFNwdGYwdXNIWlBtelBBbEE9PSIsInZhbHVlIjoiR3FSWDdMTVB1bHZ6WHdKU1VkTjliQW9DUGV3Y05RR2ZDajQwTXZZV3Qyd1NoMWJ4MVgzeTVrTzhUT3Q1bUxiVWU5bDViSzV1a0Q1Wno1T0ZFOWoxUGdYNXZLdUtObU5EK1JObUNxU2xpczVPKytJOWhMSXAva01PeXM2VXk0d0EvUWU4dko4eEJPNE1zRTBxZFd0eWxzbklmUWd2UmpvdGJQc2d3TlFnTDUxVVpuUEZvQlRZQ2JLK1VVeDJuTUp5cFI1dVRaeXkxeE5Gd1JRYzNQcXJ0WWRNWVNRWnBYQy9kcTgwNnU0a1htUGFTTjhCbGsxRHRIMk03S0pxcW91c0MrZ2w2Nk9nVFpvMlEvMEREdkxubndTRkhnYVJPRXV6a3llRUd6T2RRSUVOSVZPMng4c2YxUHhMREh1UUtDWktGVm5zdS9IQmQrQnV4LzM4SGd5VHU2MHYycFNmb2crVFdtclVEQXh4bS94SG53RWRicVE1eHVDUHJPalhmQkR2UDZwMy9tc1B0OGxHalROdm1tQ3VoamFlYjJxRmt6VUhRYkxsd09QT0p2ckM0MGd6WmhmT29zN0s3cFdMMUtpQ1l5T3JldThZdzczK3BrajlyUDBNT2RZcDFTWi83SE9hMFE3Umk3NnVBUHRGYlJrRVZxbXpMOFJqRThHR2s3TXoiLCJtYWMiOiJkNDViMTZiNjY3MTA1NjhmOGRlZTVmNjQwNTU1MzQ1MzRhZTkxZDg4YTc0ZTc1MWVlM2M0YmE1MjYzNzYxZDIxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:30:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InRTTzUwMmlDb1RaQm5pN1RqaStNcWc9PSIsInZhbHVlIjoic2xra3dWSXMxY01JSElqQ1d1azlJZHc3c1UxbWpkK2FGOG81VUZVUjJUUUljSFpJS0dIbUJUeFZGenYvSE9wZFE0SER6QzlxSFErdjR1S2tPak5IN3JaT1ZBY0FjK0JGczNKWklrNnJxT1FvUXdpMHNUalBDM09UQUVheENqdW9HcTJlNVByd1EwbW13WTUyL1lPaHJWeC9XUHZReXVlVzZORmNWalg2MmVGd1BOdXVCeUh3OVJ5MVdPZTF1SkNocCt0VXhGUGt2Sys1Vzd6OFVmV0ZxMG5GM1dWWmk4QzhtWHZuTThZVVJLendKc0xtU0NsZml0M2dLL1VQZWRjRUlJaUVRTXlUenN2UWh0aEVoOXpqVENyeUdGQ0Rmd0ZIYkZiUmRrL2ZML1NBSFVPVXdFeHIzM2hDVSszNng5QzdsdnZLNmo2c3VHa08xZzFHNHo0Z3ZqdFJIUFJGQ1RxMHFTTmhGeDJWbTVFU3RaR053ZWQzdGhIMXBxUGxNSHBTZWR6QVhQYWpGTzF3ODB6L21nWGxjS3l6UzYxNUphRS9EYndyalFvbW1aRzZZN2E3bHVLMmxuM2FhUTBybkNJeER3Z3NVWFJpUTVENzUzS1gyVXA1UStzS3RudnNTeXJBb1kvUGVBbUhEeXBNUUtLcy9zd1ZFUVJEVngxbWZTL0oiLCJtYWMiOiI1ZDVlZTg2ZTRkM2EzOGIwODJjMTg1MDMzYjJlN2YxMmRjMmFlZjA0ZTg3MWQ3N2Q1MDA3MTA1MmZhMTFjM2VmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:30:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFLdWJoTFNwdGYwdXNIWlBtelBBbEE9PSIsInZhbHVlIjoiR3FSWDdMTVB1bHZ6WHdKU1VkTjliQW9DUGV3Y05RR2ZDajQwTXZZV3Qyd1NoMWJ4MVgzeTVrTzhUT3Q1bUxiVWU5bDViSzV1a0Q1Wno1T0ZFOWoxUGdYNXZLdUtObU5EK1JObUNxU2xpczVPKytJOWhMSXAva01PeXM2VXk0d0EvUWU4dko4eEJPNE1zRTBxZFd0eWxzbklmUWd2UmpvdGJQc2d3TlFnTDUxVVpuUEZvQlRZQ2JLK1VVeDJuTUp5cFI1dVRaeXkxeE5Gd1JRYzNQcXJ0WWRNWVNRWnBYQy9kcTgwNnU0a1htUGFTTjhCbGsxRHRIMk03S0pxcW91c0MrZ2w2Nk9nVFpvMlEvMEREdkxubndTRkhnYVJPRXV6a3llRUd6T2RRSUVOSVZPMng4c2YxUHhMREh1UUtDWktGVm5zdS9IQmQrQnV4LzM4SGd5VHU2MHYycFNmb2crVFdtclVEQXh4bS94SG53RWRicVE1eHVDUHJPalhmQkR2UDZwMy9tc1B0OGxHalROdm1tQ3VoamFlYjJxRmt6VUhRYkxsd09QT0p2ckM0MGd6WmhmT29zN0s3cFdMMUtpQ1l5T3JldThZdzczK3BrajlyUDBNT2RZcDFTWi83SE9hMFE3Umk3NnVBUHRGYlJrRVZxbXpMOFJqRThHR2s3TXoiLCJtYWMiOiJkNDViMTZiNjY3MTA1NjhmOGRlZTVmNjQwNTU1MzQ1MzRhZTkxZDg4YTc0ZTc1MWVlM2M0YmE1MjYzNzYxZDIxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:30:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InRTTzUwMmlDb1RaQm5pN1RqaStNcWc9PSIsInZhbHVlIjoic2xra3dWSXMxY01JSElqQ1d1azlJZHc3c1UxbWpkK2FGOG81VUZVUjJUUUljSFpJS0dIbUJUeFZGenYvSE9wZFE0SER6QzlxSFErdjR1S2tPak5IN3JaT1ZBY0FjK0JGczNKWklrNnJxT1FvUXdpMHNUalBDM09UQUVheENqdW9HcTJlNVByd1EwbW13WTUyL1lPaHJWeC9XUHZReXVlVzZORmNWalg2MmVGd1BOdXVCeUh3OVJ5MVdPZTF1SkNocCt0VXhGUGt2Sys1Vzd6OFVmV0ZxMG5GM1dWWmk4QzhtWHZuTThZVVJLendKc0xtU0NsZml0M2dLL1VQZWRjRUlJaUVRTXlUenN2UWh0aEVoOXpqVENyeUdGQ0Rmd0ZIYkZiUmRrL2ZML1NBSFVPVXdFeHIzM2hDVSszNng5QzdsdnZLNmo2c3VHa08xZzFHNHo0Z3ZqdFJIUFJGQ1RxMHFTTmhGeDJWbTVFU3RaR053ZWQzdGhIMXBxUGxNSHBTZWR6QVhQYWpGTzF3ODB6L21nWGxjS3l6UzYxNUphRS9EYndyalFvbW1aRzZZN2E3bHVLMmxuM2FhUTBybkNJeER3Z3NVWFJpUTVENzUzS1gyVXA1UStzS3RudnNTeXJBb1kvUGVBbUhEeXBNUUtLcy9zd1ZFUVJEVngxbWZTL0oiLCJtYWMiOiI1ZDVlZTg2ZTRkM2EzOGIwODJjMTg1MDMzYjJlN2YxMmRjMmFlZjA0ZTg3MWQ3N2Q1MDA3MTA1MmZhMTFjM2VmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:30:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518578669\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1750608249 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1750608249\", {\"maxDepth\":0})</script>\n"}}