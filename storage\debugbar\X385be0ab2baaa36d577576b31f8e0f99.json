{"__meta": {"id": "X385be0ab2baaa36d577576b31f8e0f99", "datetime": "2025-07-30 09:35:53", "utime": **********.168925, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753868152.262101, "end": **********.168951, "duration": 0.9068500995635986, "duration_str": "907ms", "measures": [{"label": "Booting", "start": 1753868152.262101, "relative_start": 0, "end": **********.050334, "relative_end": **********.050334, "duration": 0.7882330417633057, "duration_str": "788ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.05036, "relative_start": 0.7882590293884277, "end": **********.168954, "relative_end": 2.86102294921875e-06, "duration": 0.11859393119812012, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665800, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=977\" onclick=\"\">app/Http/Controllers/FinanceController.php:977-1036</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01719, "accumulated_duration_str": "17.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1182141, "duration": 0.01557, "duration_str": "15.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 90.576}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.149009, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 90.576, "width_percent": 4.887}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.154862, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1003", "source": "app/Http/Controllers/FinanceController.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1003", "ajax": false, "filename": "FinanceController.php", "line": "1003"}, "connection": "radhe_same", "start_percent": 95.462, "width_percent": 4.538}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-1812176601 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1812176601\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-533705386 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-533705386\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-197893549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-197893549\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-274496204 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InRZV1ZCZE1kVzFlbWNQVlFkcjB1THc9PSIsInZhbHVlIjoiUkNVRUpHVWRPTllzTWVDWEV2RWswUFByeUhuTGxUYWw2R1cvVUZiZWpUeGJyMGJoa3Fnck94VWRSVEsxdDY0bTlTSWFGeU5DS1VVekN6YzhLWm1GdjBPR2RIc0lLN3pLYUdoWVRRckZva3ZsOG9lWGl0TzFHLzdVL042MS9WeGpERlNMaHJ0d0RHYWVJcXRnT0FrTmNOUHhqZ1NVL3FoZ0RlYW5HRWY2eEhFZHdJQy9meE9sc1ZBZTU4MVg5UVMyREJHaDlEdUxuWVFwUmpiM2tMN3VqRVRpZXQzT0dYQ0Q2aWFPUU9vQU1uWmhLU2Rnb0JoM3VhbXU5WktMQXE4THJjVHRBcWhmSlRLamVOUk9vVGZ0d3lxQkIraWZITEVkMXJGSlZpWURHaUsxS0hRaXJ3bkNXdkhEUlJsSHQ5NDJsN3J3UFQybHZtdkJWc2NVbzNGbGhDcnBNMjluaEROK3ZIckR6OG00cWJXUWpYTW1peTc2TG1Da2lzWWVBb1RFUjNyOGRzV1FaSEx5L0ZwL25aVVBmQkhZVmh1bTJ2YTd1RDRuWGZMSGxlcEJEcWs3dWVMVW9GeUpJSW5KWHdkOWtnMEpwanc2bEN5ZWRvM2ZaaFU2QjZrWkp1OWpqM3RDcjVKdUljQTgyblZOSUhaa3o0dXZjSGVQTDJrSEIwcGYiLCJtYWMiOiI0NmQ4ZDQ3MGIxNmI5M2RmZmYyOTg1N2QxOWVhN2Y3YmIwYmE0ZTk4YTRmZDUzOTI0OWUzZmY0NDI2Mjg1NTZjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InNiRWUvRVZCVEtieTNIUkh4aThrb1E9PSIsInZhbHVlIjoiN2V3VlZtTFJuVTFmTWpQalNjLzRYTjZVOTBqWFB4Zk9hNU1Xcm1sL3dSS01JZHdqUURTTWEzVmUrM2dNZ2MvTFJYOTNZVUVUcjVoY1NJeWNKMmF3VkFWLy8xU1N2WjVKWUNpVkNIWmZCdWhmckJkYkdJNWY3VEQra0M3V1pMdGN1SXliYlhmOUpXNE53S1dmdHVzcWJnSzd4QnhyMmRzQVRJM3F4TnBEakdLUERKVjBab0FTZXI1bitwNUZReGoyVmVUcmRhNS90RExhU1RjcWxCVFdWNi9zSG5iU3RKSDJSeWQ2QWMrd0krMTBtbnZOK05tQ0pkMDh2WjZlaVkrZkhkeUw3djVBWDkvQ2FVdTRBQjA1YWlRcHFoaXgvdmo5U0FmZnRlV0N2V0xWR1ZJU1lyRHd1N1A4TjgzUEFhNngzNzVoTDV0MDNQY2VXRytUN0RhaWpvUURBdDJDU1BheXkwbmt2TVZCNWI5bm5nQjl6NncxWkg4b3dNMmZHdTk5cmpnSTgzVGdxZ1R3bWJhekxFVmFhSW9XOEtXWWNrVTVEZmJjZEdkZkxXN1IvcjUwLy9SQXBxM28wVUdnSHBia0pKSFI3VHBGVG0ra2FWTTMxRkZJYkZaMWl5ZU5TMUZwNndtN0dZUUwwVGc0djNPSDFxWW5ISVdZQkJzZFBSbDkiLCJtYWMiOiIwMzRkYTkxMTM1OTk0ZGU5MjJjYTQ4ZmRhOWFkZDMzZDNiYTE0ODI4MWFkZjExZDhmNGI1OGNjOTgzZDczMzdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274496204\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1727611934 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1727611934\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:35:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InovOGYzMDF1UTBvZm11aVZmSTVLYVE9PSIsInZhbHVlIjoiRVJJZ3kxa3ZVaUFuMzVHV0tUWWZ4T1VPcEJ2cE8wZTcvdWlOTjd5SWhjaHhBL1hhV3hUT2lhTUpobmxyOTlZSEtZellHdVVWenRQMGdzVk1ONUZ0dUpZNTlxVU1KMVo5VW1iQjVBVmFxZ1FDNy9BejkrcXE5VDBZRFlwWnllVWkzd1FpVkpndnc2SXdkQVd0RmZWRExRSWUyRkRSZVpvVE8yaEMrT3BMZFV2RWIyWEZiVVZBR1hZZWZFbHpYSzN6WU83bmw2UEFZUFNIcUxFMkhGTTNVNTg4TGM0Sjh2SkFod1F5Uytpay9mNllZZGpaY05xV1JLUkVHVENqejBGWlpTcHVPM1dmMnRkM0pjZDdIemJkVk11MXhZdVNoZUVabDJ4Y1NscncvSEFyRVBHUkdnSnhkdmFVT3NHSHc3K2p2TVJ1SVZJNG44M2cxR3FjcnplSGQrOWZTekJERHJvR1h2Y0dEV0NNdXJnMUJzanlwUTIxY2thS0N4TnFDVVRNc0QvdGlWMVppTVhXcTFwdGFleFVxSHh3c0lEbUNseHlKQVkxYUwwYnBEVXR6QllIelA2MFhTbkFxWXNKZ20xUWpEN1dJYi9zQmdWZkZCQ2JQeHI2UTZkY2ViN2R0UUFmWWxGcTltZ0xMRnZwRzBHR2c4eDhZeVpEcTI0TjYxVFMiLCJtYWMiOiI1NTcyZDNiOWVjMDlhNDliMjFkNmJiZjZlMmU3ZWE3MmIyOWJlZjA3ODU1NGRjNjFjN2ZiNzU4Y2UxNGM3YTFmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:35:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjdxSnpiclNjRDYxMjI5bklCNlZRWkE9PSIsInZhbHVlIjoiNTFaVFpKY1BJdXozR2swL1hzV2RrNzE0bmhDZnhXU3QxaVZHYlV4NWZ0dldSUFdsYXQrT0ppL1doMG8vdlVuUnY4Z0F0TndJdUY2bUZDNWlUSDJuS0FNcmVxUnVlZWxpTENrbm9LTjAybHRBMGZyRGd4NDhtQnJNTWVkMnlkL29lZjFldlBYMnJXWDNMN2xCUTVEeE9iREM4TDlSQmxoZEk1NHQrSEZiVjJWeDhtMUc5K2hjSy8vSlVtTjNjLzJuN05zaTZDV1ZOUWZQK1AwUG1TZXdMOEZpWkIyaWZBTDk1Y1RxSmZWOVV0bjJydy81RVhsMjB6cEpUVzBrbUJpSmFqbndCbmhTQ01qRzBVRGlxTkhTamsyK3VwODk4dlNGSlgwVkxpN0xVNVpjcjZ0TlRvdmtvZG5PQXBTWUhwQnlnS3ZHQ29LQXlvWmJGUjV3Tks3b0JtV3Fhd25QQkowQWJ6eW1Id091LzBCZGZqb2xmR1dva2kvM25ZYWQ4MWFLaWxhQ0tLWDBYSjlKNEdSOFVOZE1heUppVWwzcllJRmdveGlvNFpTSG5iVWEwZUN6em9reHNwK0V6TGlvNThZRSs4a0FtU0NkNFhQYlBvcTZ6Q2pkN3ZRdE5TV0t0N0plblRwQk9QdW1kbDFZeDFXOGZLMWxTSVdFZEg5bjVNRlUiLCJtYWMiOiI3ZTg4NjQ0YzRjM2YyNDQ4OGVlYmNhYjAwNGI4MWExOTYyMGFjYzZjOGEyZGJiNjNjMzA0NzYyNzAxNjE5MzkwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:35:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InovOGYzMDF1UTBvZm11aVZmSTVLYVE9PSIsInZhbHVlIjoiRVJJZ3kxa3ZVaUFuMzVHV0tUWWZ4T1VPcEJ2cE8wZTcvdWlOTjd5SWhjaHhBL1hhV3hUT2lhTUpobmxyOTlZSEtZellHdVVWenRQMGdzVk1ONUZ0dUpZNTlxVU1KMVo5VW1iQjVBVmFxZ1FDNy9BejkrcXE5VDBZRFlwWnllVWkzd1FpVkpndnc2SXdkQVd0RmZWRExRSWUyRkRSZVpvVE8yaEMrT3BMZFV2RWIyWEZiVVZBR1hZZWZFbHpYSzN6WU83bmw2UEFZUFNIcUxFMkhGTTNVNTg4TGM0Sjh2SkFod1F5Uytpay9mNllZZGpaY05xV1JLUkVHVENqejBGWlpTcHVPM1dmMnRkM0pjZDdIemJkVk11MXhZdVNoZUVabDJ4Y1NscncvSEFyRVBHUkdnSnhkdmFVT3NHSHc3K2p2TVJ1SVZJNG44M2cxR3FjcnplSGQrOWZTekJERHJvR1h2Y0dEV0NNdXJnMUJzanlwUTIxY2thS0N4TnFDVVRNc0QvdGlWMVppTVhXcTFwdGFleFVxSHh3c0lEbUNseHlKQVkxYUwwYnBEVXR6QllIelA2MFhTbkFxWXNKZ20xUWpEN1dJYi9zQmdWZkZCQ2JQeHI2UTZkY2ViN2R0UUFmWWxGcTltZ0xMRnZwRzBHR2c4eDhZeVpEcTI0TjYxVFMiLCJtYWMiOiI1NTcyZDNiOWVjMDlhNDliMjFkNmJiZjZlMmU3ZWE3MmIyOWJlZjA3ODU1NGRjNjFjN2ZiNzU4Y2UxNGM3YTFmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:35:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjdxSnpiclNjRDYxMjI5bklCNlZRWkE9PSIsInZhbHVlIjoiNTFaVFpKY1BJdXozR2swL1hzV2RrNzE0bmhDZnhXU3QxaVZHYlV4NWZ0dldSUFdsYXQrT0ppL1doMG8vdlVuUnY4Z0F0TndJdUY2bUZDNWlUSDJuS0FNcmVxUnVlZWxpTENrbm9LTjAybHRBMGZyRGd4NDhtQnJNTWVkMnlkL29lZjFldlBYMnJXWDNMN2xCUTVEeE9iREM4TDlSQmxoZEk1NHQrSEZiVjJWeDhtMUc5K2hjSy8vSlVtTjNjLzJuN05zaTZDV1ZOUWZQK1AwUG1TZXdMOEZpWkIyaWZBTDk1Y1RxSmZWOVV0bjJydy81RVhsMjB6cEpUVzBrbUJpSmFqbndCbmhTQ01qRzBVRGlxTkhTamsyK3VwODk4dlNGSlgwVkxpN0xVNVpjcjZ0TlRvdmtvZG5PQXBTWUhwQnlnS3ZHQ29LQXlvWmJGUjV3Tks3b0JtV3Fhd25QQkowQWJ6eW1Id091LzBCZGZqb2xmR1dva2kvM25ZYWQ4MWFLaWxhQ0tLWDBYSjlKNEdSOFVOZE1heUppVWwzcllJRmdveGlvNFpTSG5iVWEwZUN6em9reHNwK0V6TGlvNThZRSs4a0FtU0NkNFhQYlBvcTZ6Q2pkN3ZRdE5TV0t0N0plblRwQk9QdW1kbDFZeDFXOGZLMWxTSVdFZEg5bjVNRlUiLCJtYWMiOiI3ZTg4NjQ0YzRjM2YyNDQ4OGVlYmNhYjAwNGI4MWExOTYyMGFjYzZjOGEyZGJiNjNjMzA0NzYyNzAxNjE5MzkwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:35:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-219080025 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219080025\", {\"maxDepth\":0})</script>\n"}}