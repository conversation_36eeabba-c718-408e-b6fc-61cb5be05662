{"__meta": {"id": "X86bd63eae6725620990660ca9c87979f", "datetime": "2025-07-30 09:11:24", "utime": **********.009466, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753866683.135807, "end": **********.009513, "duration": 0.8737058639526367, "duration_str": "874ms", "measures": [{"label": "Booting", "start": 1753866683.135807, "relative_start": 0, "end": 1753866683.893921, "relative_end": 1753866683.893921, "duration": 0.7581138610839844, "duration_str": "758ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753866683.893937, "relative_start": 0.****************, "end": **********.009516, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3034\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1860 to 1866\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1860\" onclick=\"\">routes/web.php:1860-1866</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gDigIFFY43zRHsuABKqfk4iT0uSmr3MVMDsODVYi", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-817210618 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-817210618\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-157427781 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-157427781\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-14678161 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-14678161\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1976643845 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976643845\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-639837459 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-639837459\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-126011477 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:11:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRHaEFLb1VmMDBiYW54azQyNlZPeEE9PSIsInZhbHVlIjoiekV4VGd1SkhiV2c5cjBQNnBvMDdhTnFLOU44QjUxekZSSnlQSlc0OHNCZElZeGhhNEROaGVTZXVwZ09vN05UNkN2SEZNZE1HYWpWb0hSdUJRaWxsMDVmeFkza0pkaHBmOU1GcGs1RUtXajJMVFM5SWJ4RWkva0tydGJMWHdqWEszZjZHaFd6OXJiSzFXQzRMRW5YelBHUlBNQkZVRitUV0ZacnJDVTNiUzIza210dlpUMzJZejA1bG45RnlOQmg3RVRFd3pmdEdrVUY4UXRqNUhSMWdsWTJDM2ZOL2hTVnVxcUJ0bC9HWmZ2b2dIcnVqUjFaeStEMVZkRzlaSDZ0b1lwRWFEeExzY2xhbVFVa1NYcXlsVEI4TldXWTJRQklPejh0a094cjhKQ2lsTytyMmJkOC85MC83UFFDOGdpZ1NEcVdvVDRRQy9RdVZac21jRkt6YlQ2b29FU29aUkpHRVBEakx0ZHY4b0krcXZhdzY1UDQ5NlJJUEljY0JuQzVYS3NWQTUram55VExjNUlEWXBURlJJbTJYK0g2MkFGQTFNRWx3V2VxZm1rUk04SXM1d3daeWNlakdUNzROQVVwWWprL0RDb0ZzV3A5Y09uclluaHFDUG9MZW1mbEdseWpRbGJ2UGFrNm9ONVAxWWJ2N0xGaFlac1FKTUtTSnVhY3IiLCJtYWMiOiI5MWM5MGE2MzU2NmFkYmIzNDI5OWIzODg0OGRiMzNlZjk3YjU4NzIyOTVjMjU3YzM3MzZkMmFmNmIwN2UzNzgwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:11:23 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlVqbE53YTVkZzJmNDZBQkVEQktWRnc9PSIsInZhbHVlIjoiQ2tzQWJSUUFNenR0NXlFbnFxTU53Zll2YlBTemVlT29oazlMa2R1RWx0V0IvbnFYemFTSW5iSFFmZHg4UEhIV3l3dU5HSU93MnRPWWtFYURGV2xnL0lSYUQydXBxaUlXWGF0Z0t2V3JLWGFEemR1NVc0eHlpRHBic0RWZkVGTFZIUnZ0czBtT2duMmIveEhycjc5cEtaSERZUzZIUkhtdkl6R1RQNGp1dHF6c09ZK2swdDRBVDlaRWFnR3ZJSXhxTHhKSFVuNWV1N1I3THZSelp5TFhDRjdrRjJMOWF4N211OWh6T2ZpNFZzRk83YmduaHNZNnp1Ym1FUC93M3k2SzBoS0dHOGlCbWw4NC9ua1hlUjFZTVJBOHI0ejhZUzNDUXRRMXptRnFoK2dUZnoyYk03bTc2OXlXeUhOQXJLOEkwcURZQ2ExYlhsai9zUUkrY3hPQTJFUE5BZFZPT2lVaTRNOXR0VG00a1F4ZWpqNlBhdnNMTkZLY2pZa1BicHgvNys5SlZJbW9ES2h3MFM0Z2NrVVNHTnBiVVlxbC9qSmpkMStnWTFEYzIzaEROSFFsV0w0M1hQTE1QSzVLNE92R0pHMHB0TmFFbHhheXc1Z2M5NlRzdWVLVk9MVzdNWWE0ZHhHSXJEVGhKZnpyVUNidVFUbGtUQ2UzVHFXUGlmeGEiLCJtYWMiOiI2YzA1YzBjNzQ4NTg2ODg2MjA0MzQxYzJiMDE4NjIxZmUyNWFiYTBjYjcwYzJlMmRlMDlkMzc4MmFmMTI1MTVkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:11:23 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRHaEFLb1VmMDBiYW54azQyNlZPeEE9PSIsInZhbHVlIjoiekV4VGd1SkhiV2c5cjBQNnBvMDdhTnFLOU44QjUxekZSSnlQSlc0OHNCZElZeGhhNEROaGVTZXVwZ09vN05UNkN2SEZNZE1HYWpWb0hSdUJRaWxsMDVmeFkza0pkaHBmOU1GcGs1RUtXajJMVFM5SWJ4RWkva0tydGJMWHdqWEszZjZHaFd6OXJiSzFXQzRMRW5YelBHUlBNQkZVRitUV0ZacnJDVTNiUzIza210dlpUMzJZejA1bG45RnlOQmg3RVRFd3pmdEdrVUY4UXRqNUhSMWdsWTJDM2ZOL2hTVnVxcUJ0bC9HWmZ2b2dIcnVqUjFaeStEMVZkRzlaSDZ0b1lwRWFEeExzY2xhbVFVa1NYcXlsVEI4TldXWTJRQklPejh0a094cjhKQ2lsTytyMmJkOC85MC83UFFDOGdpZ1NEcVdvVDRRQy9RdVZac21jRkt6YlQ2b29FU29aUkpHRVBEakx0ZHY4b0krcXZhdzY1UDQ5NlJJUEljY0JuQzVYS3NWQTUram55VExjNUlEWXBURlJJbTJYK0g2MkFGQTFNRWx3V2VxZm1rUk04SXM1d3daeWNlakdUNzROQVVwWWprL0RDb0ZzV3A5Y09uclluaHFDUG9MZW1mbEdseWpRbGJ2UGFrNm9ONVAxWWJ2N0xGaFlac1FKTUtTSnVhY3IiLCJtYWMiOiI5MWM5MGE2MzU2NmFkYmIzNDI5OWIzODg0OGRiMzNlZjk3YjU4NzIyOTVjMjU3YzM3MzZkMmFmNmIwN2UzNzgwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:11:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlVqbE53YTVkZzJmNDZBQkVEQktWRnc9PSIsInZhbHVlIjoiQ2tzQWJSUUFNenR0NXlFbnFxTU53Zll2YlBTemVlT29oazlMa2R1RWx0V0IvbnFYemFTSW5iSFFmZHg4UEhIV3l3dU5HSU93MnRPWWtFYURGV2xnL0lSYUQydXBxaUlXWGF0Z0t2V3JLWGFEemR1NVc0eHlpRHBic0RWZkVGTFZIUnZ0czBtT2duMmIveEhycjc5cEtaSERZUzZIUkhtdkl6R1RQNGp1dHF6c09ZK2swdDRBVDlaRWFnR3ZJSXhxTHhKSFVuNWV1N1I3THZSelp5TFhDRjdrRjJMOWF4N211OWh6T2ZpNFZzRk83YmduaHNZNnp1Ym1FUC93M3k2SzBoS0dHOGlCbWw4NC9ua1hlUjFZTVJBOHI0ejhZUzNDUXRRMXptRnFoK2dUZnoyYk03bTc2OXlXeUhOQXJLOEkwcURZQ2ExYlhsai9zUUkrY3hPQTJFUE5BZFZPT2lVaTRNOXR0VG00a1F4ZWpqNlBhdnNMTkZLY2pZa1BicHgvNys5SlZJbW9ES2h3MFM0Z2NrVVNHTnBiVVlxbC9qSmpkMStnWTFEYzIzaEROSFFsV0w0M1hQTE1QSzVLNE92R0pHMHB0TmFFbHhheXc1Z2M5NlRzdWVLVk9MVzdNWWE0ZHhHSXJEVGhKZnpyVUNidVFUbGtUQ2UzVHFXUGlmeGEiLCJtYWMiOiI2YzA1YzBjNzQ4NTg2ODg2MjA0MzQxYzJiMDE4NjIxZmUyNWFiYTBjYjcwYzJlMmRlMDlkMzc4MmFmMTI1MTVkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:11:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-126011477\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-177950423 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gDigIFFY43zRHsuABKqfk4iT0uSmr3MVMDsODVYi</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177950423\", {\"maxDepth\":0})</script>\n"}}