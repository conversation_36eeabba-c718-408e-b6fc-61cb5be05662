{"__meta": {"id": "X490d58e0ac6e992e522a79b947520f1d", "datetime": "2025-07-30 09:12:23", "utime": **********.398227, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753866742.301491, "end": **********.398267, "duration": 1.096776008605957, "duration_str": "1.1s", "measures": [{"label": "Booting", "start": 1753866742.301491, "relative_start": 0, "end": **********.304502, "relative_end": **********.304502, "duration": 1.0030109882354736, "duration_str": "1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.304518, "relative_start": 1.****************, "end": **********.398271, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "93.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JfdciqnV2Je9wn2EZqiBrBYXNWJtoV9yD53TE1Fx", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-22328490 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-22328490\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1301658196 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1301658196\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1196026760 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1196026760\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-279409648 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-279409648\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-673125903 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-673125903\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1208539165 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:12:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNhZHhvQ2VBTlVFSXJKSngvSFd1M1E9PSIsInZhbHVlIjoiYlptSXdSYzFRa3JEZ3BnWFVuSE9naENCRThoNXlTbkFLM3JkTmFsVUdhWjRyRXM3c3ZoSEhpMzUrSjdqV2NSUmlzNS84UDdJVGh0N1VFcTM5R0E1K3FJZzdsckJWVWZvUEZUcXVuL0pwdW5paUg4a0JWdlVSVGRnazZrdTI0VFkrTjRJM0taRVRHMnkxcmVOdEtuZlhoVmxadmVtOXlVS1J3Qzd4MlpuTE5aeENtYmlGRzhPT2xBNUVnQnVqWmIrRHhyekpKYWM3dWJseTVjNWliZ2liWGk0NVUwYk01UmFtSjgxL3dzZkVDZWJDV1UycXdmTHJuMDQwRzNIU2daNVNXRGt5Z1RJS2NGWG5GM3FHbm9BZzdadlVjbHZ5Q0FtTXlkb1ZGM2k3OWFiSnd1amVyeTF4aDM0dzlZdkRENENOTCtCSS9XWUZLMDl1d1M4RWJVVWM0bDFNWklkM2NaT08rZFRvV1dGd2NQK0tROGZrM05RWS9sSTVDbXJpa0prbXdFRmNsamN1TGNwdmw1Z2ZLSld4MlB3NzE2MmVFeVQ2R29Yak1nSkFuSXZDRE5IbEx4MHJybklrYUVlaXhLUU5yblRpcndvNFgvS1M3SWlwTTdnNkpVb2pRL3ZGYkxzSnRJdFJlQTk2emcybUVHN3NzS1ZidG9UejY1Q1pZSFEiLCJtYWMiOiIzYzcyMGU2YWM5MGZmMmUwNzM1NTc2NTI1YThhNDFhN2RlNTMwOTJjMzIzZWU5MjM5NWVlYzY1NWQxMTc5ODk4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:12:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVEeUZXeitWRFNCaTEySWdFbE15Tmc9PSIsInZhbHVlIjoiRjdITDd3Y25QQ09YdFBudm1hMUVaWllvQi96MGUwWXg5TlhBM3Y2WGU0STN0bDROY29nUkxCdUhFSnlvMVlvd21UQUVmenpvT2NwWjFETnd6ZjI1a2U5OThxVXBvKytUVStDWTUrNHA4b1JnYWlCaFpRUE1YdkhkTXpWN014YmxKZFAwSzJsWlZROStuUWpGV0hIdFQzT2RLdkYvTmEvR2xsL1dubWhNeG1jN0lhOTNwRVJoQ3FRcytsVzFLWTBmV0tpSk1xanlmdk1uOGNwRkVmUjVIc1VHZEJ6WlpjR2RMbmNMeGtYWWUzNDdGNGJqb2JmOGxTZTFKYW9Wcm4wMGJ6VFBQUkt3ZC9HSjRQSWt5VXoyWndoOW9HdG9vZTAvcG9ydG1GM1RrSE9qbUM5UG14NXZ5WG40NmhUM1VTVEtubTNodU9HVm9yY0Q1bjlSejQ0d2hFcUZNVVVmWXpTN0JueXBpbzBLTnZEMVg4aFlxUVlralpTMkI5bVpqSnZXbmVUSStPUGQrV00zS09VeWRLaEFYc0N2MlNSdVFyS3VORGNPMnkwZkhmM0JYWDRLWHdNQi8rbS8wVkVIRUNRVDAwb1VqRkFIczJqMjYxZU9yaGdROU5NNGc4S3M0RW5aTStPdUJwY0V4U0pVRDg0L2I1UXJTOCtYSm5OU1dEVHkiLCJtYWMiOiJiYjZjN2M4NzY1OWM0YWZlZGI5NjViOWQwOWMyOTE5NzI3MmI5Y2ExNDU4MWQyMDA2OGU4YjdiMDNkYzdjYzY3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:12:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNhZHhvQ2VBTlVFSXJKSngvSFd1M1E9PSIsInZhbHVlIjoiYlptSXdSYzFRa3JEZ3BnWFVuSE9naENCRThoNXlTbkFLM3JkTmFsVUdhWjRyRXM3c3ZoSEhpMzUrSjdqV2NSUmlzNS84UDdJVGh0N1VFcTM5R0E1K3FJZzdsckJWVWZvUEZUcXVuL0pwdW5paUg4a0JWdlVSVGRnazZrdTI0VFkrTjRJM0taRVRHMnkxcmVOdEtuZlhoVmxadmVtOXlVS1J3Qzd4MlpuTE5aeENtYmlGRzhPT2xBNUVnQnVqWmIrRHhyekpKYWM3dWJseTVjNWliZ2liWGk0NVUwYk01UmFtSjgxL3dzZkVDZWJDV1UycXdmTHJuMDQwRzNIU2daNVNXRGt5Z1RJS2NGWG5GM3FHbm9BZzdadlVjbHZ5Q0FtTXlkb1ZGM2k3OWFiSnd1amVyeTF4aDM0dzlZdkRENENOTCtCSS9XWUZLMDl1d1M4RWJVVWM0bDFNWklkM2NaT08rZFRvV1dGd2NQK0tROGZrM05RWS9sSTVDbXJpa0prbXdFRmNsamN1TGNwdmw1Z2ZLSld4MlB3NzE2MmVFeVQ2R29Yak1nSkFuSXZDRE5IbEx4MHJybklrYUVlaXhLUU5yblRpcndvNFgvS1M3SWlwTTdnNkpVb2pRL3ZGYkxzSnRJdFJlQTk2emcybUVHN3NzS1ZidG9UejY1Q1pZSFEiLCJtYWMiOiIzYzcyMGU2YWM5MGZmMmUwNzM1NTc2NTI1YThhNDFhN2RlNTMwOTJjMzIzZWU5MjM5NWVlYzY1NWQxMTc5ODk4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:12:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVEeUZXeitWRFNCaTEySWdFbE15Tmc9PSIsInZhbHVlIjoiRjdITDd3Y25QQ09YdFBudm1hMUVaWllvQi96MGUwWXg5TlhBM3Y2WGU0STN0bDROY29nUkxCdUhFSnlvMVlvd21UQUVmenpvT2NwWjFETnd6ZjI1a2U5OThxVXBvKytUVStDWTUrNHA4b1JnYWlCaFpRUE1YdkhkTXpWN014YmxKZFAwSzJsWlZROStuUWpGV0hIdFQzT2RLdkYvTmEvR2xsL1dubWhNeG1jN0lhOTNwRVJoQ3FRcytsVzFLWTBmV0tpSk1xanlmdk1uOGNwRkVmUjVIc1VHZEJ6WlpjR2RMbmNMeGtYWWUzNDdGNGJqb2JmOGxTZTFKYW9Wcm4wMGJ6VFBQUkt3ZC9HSjRQSWt5VXoyWndoOW9HdG9vZTAvcG9ydG1GM1RrSE9qbUM5UG14NXZ5WG40NmhUM1VTVEtubTNodU9HVm9yY0Q1bjlSejQ0d2hFcUZNVVVmWXpTN0JueXBpbzBLTnZEMVg4aFlxUVlralpTMkI5bVpqSnZXbmVUSStPUGQrV00zS09VeWRLaEFYc0N2MlNSdVFyS3VORGNPMnkwZkhmM0JYWDRLWHdNQi8rbS8wVkVIRUNRVDAwb1VqRkFIczJqMjYxZU9yaGdROU5NNGc4S3M0RW5aTStPdUJwY0V4U0pVRDg0L2I1UXJTOCtYSm5OU1dEVHkiLCJtYWMiOiJiYjZjN2M4NzY1OWM0YWZlZGI5NjViOWQwOWMyOTE5NzI3MmI5Y2ExNDU4MWQyMDA2OGU4YjdiMDNkYzdjYzY3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:12:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1208539165\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1468770301 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JfdciqnV2Je9wn2EZqiBrBYXNWJtoV9yD53TE1Fx</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468770301\", {\"maxDepth\":0})</script>\n"}}