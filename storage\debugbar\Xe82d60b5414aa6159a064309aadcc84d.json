{"__meta": {"id": "Xe82d60b5414aa6159a064309aadcc84d", "datetime": "2025-07-30 09:29:43", "utime": **********.627972, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753867782.776866, "end": **********.628, "duration": 0.8511340618133545, "duration_str": "851ms", "measures": [{"label": "Booting", "start": 1753867782.776866, "relative_start": 0, "end": **********.545954, "relative_end": **********.545954, "duration": 0.7690880298614502, "duration_str": "769ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.54597, "relative_start": 0.**************, "end": **********.628002, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "82.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3033\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1860 to 1866\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1860\" onclick=\"\">routes/web.php:1860-1866</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A1goqk158IzMANdELJdanCCUF7ZSiGz5eaW4VYYt", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-287933582 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-287933582\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-352688213 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-352688213\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-27077825 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-27077825\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1220084354 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1220084354\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1605490519 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1605490519\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-61953149 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:29:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVaeXNIUktMRzN0ZHNYV01ncDZsYmc9PSIsInZhbHVlIjoiN2pnSGdhMk5kenI3TmNzUTBoRmRyWmVVUkZhZ0l0ME9aR2xEWXlSZklJcDl0eUFOZW5YcjE1MzFETXZqa2lWc0h5clpBK3VOU0RFbzdReWZ6NTRVQlB6cTh2M2FHZlIrSmVWcG9MWStsWkVqMVMzTmJzWE5KZGRmWGx3RktHN0F3RUFGcG5pcU8rRjNnbUxKWTl3N0tCMWdrMkt6NzB2cnVaWUZIVDExbnUzYXBZUlJUSVlrdWpobnpyL0J3VXV3R3FtbUY3UHJCREhFVHhnNm0rM2poTlZqOXpPa2NQN01uTmZJMXhsRkNnbTRwTnZBazlQVXE3SHNWT3BFaGYwQU5OZWlHbHJhV0drK1pvWEM1eGREaXZVQjNNNkNwdGpyc0d3T1FWdEY2T3IwMWJtM2JKUUExRTFkQ2FtelpIcW9ZdWpPamlNdGpaU1FyT011K2VmWHNUNFovcEpjb0lKZHp5U2JyT0l6Tk90L2YxRzlCVXZIakEzTFNzRFQ1T2lSRVBtc0dMNUVjQkZlSE5SdkRWTVhQV3hEazFkZzlDNTNrQ3dVTExadVlUTXdtd0tYT2gyV0d5Qmc1ME9qTVhzT1ZOdldwL2h5eWc0cXNZcys2MWxxOHRtbVdiN3gxMmFGU1A1MVliTXhTMW00cyt6UGpLSlNEc3VFRGc1T1hiV2oiLCJtYWMiOiI5Y2UzYTdlM2IyYzE4YTMyZWVjZjNjZTIzYjdiN2ZmZTg0ZjZjMTk5YTFhNWY2ZTcxYzY2NDMyZDQ2Mzk5OGNkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:29:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImwzOWoyRjlGbkpxc3dJdzJwZmpOR0E9PSIsInZhbHVlIjoiT3BINENtK0tLRE1NYmU0ckRIRHY4WFVwYTNsZTQ4WnA2M1pSOFhEdE9LTlY5YTYyNzRQMzBiMFlxQ1VjYjY4TU5NQitwUXc2RXc0cmZlQ1QzaUo1cTZOcjF6TzhYaWQ2UGNzSnBkb3M5MnJ6cXgvRHovUGJaUDVRWHk5ZHJueDRGdGhqNjRaUEpMa0ZXdUtXeDhHcFJGTS90N1pLV3h5amdwakJXaWd1WE0wRGg3UWhPOS9NUHNyRGpiNnR5VHl5aDJiZkpTNlJFalJLSlRQb0RvaXlFRExMVHZENmhNZEw5YUdweWRESDZkYzlLZHZzcFhSdEpBRTZWdVJvY1lXWlc0bVRKVmNkdFhsZzFpYlZ2N0c5WWxsdDJTZHNXMVR4SFllTTV4NDhUd29jakRNMk9rak4vQzBaTlgxQ2xPOEZqdUtSaUtwaU1JZS9DZFVrcjArRVRuZjlvemR5T1grSkdLS1AybmxmWFpNZ0huVlcvbTRDTmxFK0pSNU9hY3NVdVJQNXowZE1IVzExSHlyYzNQYjcxZk5yV3czVTdUWHNNTVhiN0VscmJYblhVMGo4TFRzR1VLVU5iNjcyR3BwMFBwUENzekk4dXdreG1xcVF4MWFOckhKQ2UrVlV1RnFsdmdPS09TKzhCWE1Fc1diSlNxSzJYY2R3cHpDbE1tRUsiLCJtYWMiOiIzYzlkNmIwMzgxNGZjNDRkOTBlNjdhYjA5MWFkYmUwZDEwMDRjNTNhMzJlMTI5NDkwYzYyZTI3MmYxYWMwMmIwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:29:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVaeXNIUktMRzN0ZHNYV01ncDZsYmc9PSIsInZhbHVlIjoiN2pnSGdhMk5kenI3TmNzUTBoRmRyWmVVUkZhZ0l0ME9aR2xEWXlSZklJcDl0eUFOZW5YcjE1MzFETXZqa2lWc0h5clpBK3VOU0RFbzdReWZ6NTRVQlB6cTh2M2FHZlIrSmVWcG9MWStsWkVqMVMzTmJzWE5KZGRmWGx3RktHN0F3RUFGcG5pcU8rRjNnbUxKWTl3N0tCMWdrMkt6NzB2cnVaWUZIVDExbnUzYXBZUlJUSVlrdWpobnpyL0J3VXV3R3FtbUY3UHJCREhFVHhnNm0rM2poTlZqOXpPa2NQN01uTmZJMXhsRkNnbTRwTnZBazlQVXE3SHNWT3BFaGYwQU5OZWlHbHJhV0drK1pvWEM1eGREaXZVQjNNNkNwdGpyc0d3T1FWdEY2T3IwMWJtM2JKUUExRTFkQ2FtelpIcW9ZdWpPamlNdGpaU1FyT011K2VmWHNUNFovcEpjb0lKZHp5U2JyT0l6Tk90L2YxRzlCVXZIakEzTFNzRFQ1T2lSRVBtc0dMNUVjQkZlSE5SdkRWTVhQV3hEazFkZzlDNTNrQ3dVTExadVlUTXdtd0tYT2gyV0d5Qmc1ME9qTVhzT1ZOdldwL2h5eWc0cXNZcys2MWxxOHRtbVdiN3gxMmFGU1A1MVliTXhTMW00cyt6UGpLSlNEc3VFRGc1T1hiV2oiLCJtYWMiOiI5Y2UzYTdlM2IyYzE4YTMyZWVjZjNjZTIzYjdiN2ZmZTg0ZjZjMTk5YTFhNWY2ZTcxYzY2NDMyZDQ2Mzk5OGNkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:29:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImwzOWoyRjlGbkpxc3dJdzJwZmpOR0E9PSIsInZhbHVlIjoiT3BINENtK0tLRE1NYmU0ckRIRHY4WFVwYTNsZTQ4WnA2M1pSOFhEdE9LTlY5YTYyNzRQMzBiMFlxQ1VjYjY4TU5NQitwUXc2RXc0cmZlQ1QzaUo1cTZOcjF6TzhYaWQ2UGNzSnBkb3M5MnJ6cXgvRHovUGJaUDVRWHk5ZHJueDRGdGhqNjRaUEpMa0ZXdUtXeDhHcFJGTS90N1pLV3h5amdwakJXaWd1WE0wRGg3UWhPOS9NUHNyRGpiNnR5VHl5aDJiZkpTNlJFalJLSlRQb0RvaXlFRExMVHZENmhNZEw5YUdweWRESDZkYzlLZHZzcFhSdEpBRTZWdVJvY1lXWlc0bVRKVmNkdFhsZzFpYlZ2N0c5WWxsdDJTZHNXMVR4SFllTTV4NDhUd29jakRNMk9rak4vQzBaTlgxQ2xPOEZqdUtSaUtwaU1JZS9DZFVrcjArRVRuZjlvemR5T1grSkdLS1AybmxmWFpNZ0huVlcvbTRDTmxFK0pSNU9hY3NVdVJQNXowZE1IVzExSHlyYzNQYjcxZk5yV3czVTdUWHNNTVhiN0VscmJYblhVMGo4TFRzR1VLVU5iNjcyR3BwMFBwUENzekk4dXdreG1xcVF4MWFOckhKQ2UrVlV1RnFsdmdPS09TKzhCWE1Fc1diSlNxSzJYY2R3cHpDbE1tRUsiLCJtYWMiOiIzYzlkNmIwMzgxNGZjNDRkOTBlNjdhYjA5MWFkYmUwZDEwMDRjNTNhMzJlMTI5NDkwYzYyZTI3MmYxYWMwMmIwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:29:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-61953149\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1728870907 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A1goqk158IzMANdELJdanCCUF7ZSiGz5eaW4VYYt</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728870907\", {\"maxDepth\":0})</script>\n"}}