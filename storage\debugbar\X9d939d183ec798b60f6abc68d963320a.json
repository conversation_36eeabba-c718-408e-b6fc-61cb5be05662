{"__meta": {"id": "X9d939d183ec798b60f6abc68d963320a", "datetime": "2025-07-30 09:24:55", "utime": **********.400707, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753867494.495852, "end": **********.400735, "duration": 0.9048829078674316, "duration_str": "905ms", "measures": [{"label": "Booting", "start": 1753867494.495852, "relative_start": 0, "end": **********.290587, "relative_end": **********.290587, "duration": 0.7947349548339844, "duration_str": "795ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.290632, "relative_start": 0.****************, "end": **********.400738, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3033\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1860 to 1866\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1860\" onclick=\"\">routes/web.php:1860-1866</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PCkPj0XGUvpZ07MCuKGHDbztWG7HWxowLOdpoXAq", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-464427094 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-464427094\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1810020401 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1810020401\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-237891514 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-237891514\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1214513253 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214513253\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-597487006 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-597487006\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-181029980 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:24:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktCK3VCVTFxZ2E4V2lUR3UwZHNERUE9PSIsInZhbHVlIjoiTzNVbFh1MXZpR1JDM3pXa2k3a0xoWk52cVFta1VjN1NxbHV0VnhmR2lPR2VPNFBxNXVET2IyTllqSmVhaFVyYWJBcUtrS04xdVZoeEt2S1hiZWlVMm5udkc4ZHFNbTVMSFRIVVdFMFJIbjFOTkxjR0VuMjl4TVUyUkU4dmQxMXpUQjVOcWRNT2NPM2ZWd3V3c3lmYjErY1ZxeVk3T2R1a2wwSXJzWnZRWTN6N0JYOGZxVmtDQ2Q0dnFtZzZwSDYxa0hnR0lKRUpPVDkzR3BrdWdpa3hzTkZPdHdsQ3R0ZjErN25qMkRoTU9FNE1aNDR3ZGpGSi9ZOUFTeU1zZ3Zwam9mVldGWm5IMEVxWGVERWZySzdmQ1YzTHl5MmxSVlhQYmdtdGF2NTVrTm10ejV5Y0tYVEtSRTZYRDZPaEg2aVU1QVRBNjZUR0tldCtLSmFLenZ5MzFhaVlaMHRudmt5ZjBuRnlwRkxSVXNzSUQ1L0t6UTdBdmNmQWNZOGFySllHTWlac0IrWCswekk2VVN1SnIzWndpeVV3T1hiOTIxT1duL00wL0FNR3NkVlU2QkRWRlU4cWUxUVRmZm41RDR2U2JPMmxHckpFU09GSjYyMWVYMWxVM0VBc243ZzYxVjZiYnBwYkllRERqNWhyZ1ZlUkQrMWtHd0Y1TWNlM2pqcjIiLCJtYWMiOiI1ZTM1NGQ2YzI5ZmJhYmUxNzA2ZTczODhiNWE0MGY3NDMzMDQ3MDM5NGIxZDYyMzI2NWYwM2M0ZThjNGEyYjgzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:24:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjM4TjhvdFJ6VTJsdGViSTI5YVpuR0E9PSIsInZhbHVlIjoiUmZTOFFISXRYQnNvZXRnZjZaZG94amtzanU2ek5QcFFuVTkxbzNkcGhUaE9rVEVPYzMvWForTnpBOTRFV1ExV2xVcDBLZlFMM3RVZkhCcG4xbGdXZWdKMDgrNjBYS2pWZmtYR25iYnFkT1VlNDNSOVVTWWJjaFJZcEZpWXBFM2ROS3VLMW9IUTYwTndkOXVTeC9IK0dzNktGRWlIRzhvUy9GMDBsdUVCVG40cEtiYkVETkFweHFzWlRmb2RZcDhKMVdLeFE1K1J6d1B3LytzMysyaGphTkZrU3Z4MDMwMGlwZ0hmMmpjcEZhNXJ3N09uWFJRNE1yQ2ZZOUNPTnhpNCtOc0tCV0NEclUyODNDcUFRYmVBSXI2WUg3bDFISGI4WWlvRTgwd0lPMjNDQy9UQjhPRlNtSkNIbEVjUnlkc3RTVzk0bE1hRmYva0VOclhrNkErUVRod3oxVUdsVkxORXVwUWVadmQ3eUNySlN2RUxqeGdsbjlUVWNCTTZsZHEvT0ZxU1dPOHh2cmlzT1cyZkNIblJrTmpKN1Qva09GTWFNamYwcVQ3MERGZWc1OTZyb1FZM3ozemhnUSt0KzFIK2M5OFJMMGpFWSs5ajNBWm9aR1g0azBmc09zaFFsZlR5ZFJvUGNGa1kxRFRST0RqNXVkVDNGeEhTeXIwMTR1c3IiLCJtYWMiOiI1NWVjNmMzMDQwNDRjYmU4NjBjNDI1MDBmOTQ1NWI0MGQzOGFiNzIwYzFjM2IxMzZjMzk5Y2Y5YzA3NTRlZTYwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:24:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktCK3VCVTFxZ2E4V2lUR3UwZHNERUE9PSIsInZhbHVlIjoiTzNVbFh1MXZpR1JDM3pXa2k3a0xoWk52cVFta1VjN1NxbHV0VnhmR2lPR2VPNFBxNXVET2IyTllqSmVhaFVyYWJBcUtrS04xdVZoeEt2S1hiZWlVMm5udkc4ZHFNbTVMSFRIVVdFMFJIbjFOTkxjR0VuMjl4TVUyUkU4dmQxMXpUQjVOcWRNT2NPM2ZWd3V3c3lmYjErY1ZxeVk3T2R1a2wwSXJzWnZRWTN6N0JYOGZxVmtDQ2Q0dnFtZzZwSDYxa0hnR0lKRUpPVDkzR3BrdWdpa3hzTkZPdHdsQ3R0ZjErN25qMkRoTU9FNE1aNDR3ZGpGSi9ZOUFTeU1zZ3Zwam9mVldGWm5IMEVxWGVERWZySzdmQ1YzTHl5MmxSVlhQYmdtdGF2NTVrTm10ejV5Y0tYVEtSRTZYRDZPaEg2aVU1QVRBNjZUR0tldCtLSmFLenZ5MzFhaVlaMHRudmt5ZjBuRnlwRkxSVXNzSUQ1L0t6UTdBdmNmQWNZOGFySllHTWlac0IrWCswekk2VVN1SnIzWndpeVV3T1hiOTIxT1duL00wL0FNR3NkVlU2QkRWRlU4cWUxUVRmZm41RDR2U2JPMmxHckpFU09GSjYyMWVYMWxVM0VBc243ZzYxVjZiYnBwYkllRERqNWhyZ1ZlUkQrMWtHd0Y1TWNlM2pqcjIiLCJtYWMiOiI1ZTM1NGQ2YzI5ZmJhYmUxNzA2ZTczODhiNWE0MGY3NDMzMDQ3MDM5NGIxZDYyMzI2NWYwM2M0ZThjNGEyYjgzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:24:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjM4TjhvdFJ6VTJsdGViSTI5YVpuR0E9PSIsInZhbHVlIjoiUmZTOFFISXRYQnNvZXRnZjZaZG94amtzanU2ek5QcFFuVTkxbzNkcGhUaE9rVEVPYzMvWForTnpBOTRFV1ExV2xVcDBLZlFMM3RVZkhCcG4xbGdXZWdKMDgrNjBYS2pWZmtYR25iYnFkT1VlNDNSOVVTWWJjaFJZcEZpWXBFM2ROS3VLMW9IUTYwTndkOXVTeC9IK0dzNktGRWlIRzhvUy9GMDBsdUVCVG40cEtiYkVETkFweHFzWlRmb2RZcDhKMVdLeFE1K1J6d1B3LytzMysyaGphTkZrU3Z4MDMwMGlwZ0hmMmpjcEZhNXJ3N09uWFJRNE1yQ2ZZOUNPTnhpNCtOc0tCV0NEclUyODNDcUFRYmVBSXI2WUg3bDFISGI4WWlvRTgwd0lPMjNDQy9UQjhPRlNtSkNIbEVjUnlkc3RTVzk0bE1hRmYva0VOclhrNkErUVRod3oxVUdsVkxORXVwUWVadmQ3eUNySlN2RUxqeGdsbjlUVWNCTTZsZHEvT0ZxU1dPOHh2cmlzT1cyZkNIblJrTmpKN1Qva09GTWFNamYwcVQ3MERGZWc1OTZyb1FZM3ozemhnUSt0KzFIK2M5OFJMMGpFWSs5ajNBWm9aR1g0azBmc09zaFFsZlR5ZFJvUGNGa1kxRFRST0RqNXVkVDNGeEhTeXIwMTR1c3IiLCJtYWMiOiI1NWVjNmMzMDQwNDRjYmU4NjBjNDI1MDBmOTQ1NWI0MGQzOGFiNzIwYzFjM2IxMzZjMzk5Y2Y5YzA3NTRlZTYwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:24:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-181029980\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1407005425 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PCkPj0XGUvpZ07MCuKGHDbztWG7HWxowLOdpoXAq</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1407005425\", {\"maxDepth\":0})</script>\n"}}