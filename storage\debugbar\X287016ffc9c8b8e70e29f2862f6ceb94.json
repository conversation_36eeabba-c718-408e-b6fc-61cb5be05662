{"__meta": {"id": "X287016ffc9c8b8e70e29f2862f6ceb94", "datetime": "2025-07-30 09:25:19", "utime": **********.514161, "method": "GET", "uri": "/finance/sales/products/search?search=bo", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753867518.762203, "end": **********.514193, "duration": 0.7519900798797607, "duration_str": "752ms", "measures": [{"label": "Booting", "start": 1753867518.762203, "relative_start": 0, "end": **********.411701, "relative_end": **********.411701, "duration": 0.6494979858398438, "duration_str": "649ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.411715, "relative_start": 0.6495120525360107, "end": **********.514196, "relative_end": 2.86102294921875e-06, "duration": 0.10248088836669922, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46676680, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/products/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchProducts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-946</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0076, "accumulated_duration_str": "7.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4743822, "duration": 0.00536, "duration_str": "5.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 70.526}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.495226, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 70.526, "width_percent": 10.395}, {"sql": "select `id`, `name`, `sale_price`, `sku`, `type` from `product_services` where `created_by` = 79 and (`name` LIKE '%bo%' or `sku` LIKE '%bo%') order by `name` asc limit 50", "type": "query", "params": [], "bindings": ["79", "%bo%", "%bo%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 923}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.500542, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:923", "source": "app/Http/Controllers/FinanceController.php:923", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=923", "ajax": false, "filename": "FinanceController.php", "line": "923"}, "connection": "radhe_same", "start_percent": 80.921, "width_percent": 19.079}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/products/search", "status_code": "<pre class=sf-dump id=sf-dump-1091678686 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1091678686\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-246904932 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">bo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-246904932\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-87745708 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-87745708\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-992680664 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImoyODdFek1ncFF0d2N5STJQcEpRUVE9PSIsInZhbHVlIjoiSjByU21SZ0VBdVo5VlJUaVEwMmptWnJFSmpUOVZ0MmJSajFkdzl1T1ZMMCt3WkZVdHRhejJvbU5uZ21kV2pINzA3MStPd2hBay9EdWdiV1pFRHJhaTNTYjJNamlSSUhEOVZZVi9halFQQ0JEUGNMUmFlYmtMMmVmaGFMUU50R3dDWEgwVE0yOEdWN2FkUUIxOFFUSTJHbk8vTytFbUdGZG9vai9XU0doV1VDNmQrUmJpZUxvM3liSERHbTN6V2lkTDhVclYwQS9YdXg1eHhvZG9lempOOWRnMXVzNnZUdG0rZVpzeklXdWdwL0hmVzBHdHB6V3BEc1c1Rk85SHAzRmtOYUhMR2dGSTBlb216b3VwV09kWHNjYUtPS0xnODhvUnliTjZ2QXpSanpmREhHUzk2MkJyWUduUmVCUWFpandMWEJKekF2UzBONFFyUkNVaHRCdm94T1lFUHlwS0RHcUN6Q3ZWWFppNngrb2tJZ2ZKT0NWYUVNQy8zWEMrSGtVWFNDYXJEZFk5RWN6N21hQ0NyeHdXK1Ruc0ZXZHZ5dmM5cVNvQWRRVDhsRGx1RHh1SzFFdVB3ZkoxdG52N0RqN1VDeHA1eWpsakFXNWREZmV3RjlRZ1BtZ2VGRDhaTjlKKzA1NzROcW83Rk1FREt3eVlwUkgxUjQ3ZEdEK2s2MmIiLCJtYWMiOiJjOTdlMmE0MTgxMTA4NDhlOTVkYjMxZWFlN2VkNGU0MDhhYjBlNGI5YjhmMzUzZDJiZmNlZDU0YTQ0YTVlMWJkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkxKcVBUY3lZUVpOMDBERG1xOXRzWkE9PSIsInZhbHVlIjoiajhQa013SStjRjJKRk5TTk0vUEc4MzMwMm1ReEYvWkI3eXdad2VxRFRtR29FV1lQVWc5dkJLdGNFR0FkaFJEZHVSZmlNbHBGS3RsU3RKSTB2UWxXOEJuTnpHdUQyNE5wcStzY2tYdG9Ea2lpQWdXRjZudWI5eUdsOTV3MG5nTy91aExHem1zTWlrVWFjUUVCQ3VHMXlIMTg2bzF6TXFKcEgraEgza2xGWC9pdGpyNmZNcHNxUWNZeFQxM0tyNDJuS3lJU0k2RUsydkR2TnRaVDg3Y1FPVEVNZ3ZqcWRDQUtta1JVVkRjYXRrNFN4TGM5ck5PVVZrWGpBWTg2Wis3ZTNQN0VxdjlTRkF3dnJLQUR0Ymw2QTBqODIwemRYU0RzU2NvZHUwSFZVQ0ppSFRJQkhmeEFWK2hYYTE0bGZoQnV0SFBuRyswSjljTS9Ed1N6SDRUUlRHbC9iY1dBRGhNQ2N6dmRDcHExVzdCS2ZyaHpsZnNRY29CamVZOUwvYUZzR1I3OUVMUGppVmRRS3lieXkxT084Nm5BV01zeEFmdlhEbC8rTEFWYXJuaENjcnpDRVdnanUrTGZHemR1K2VCYWQ0S25yU3B5Z1BteGF2dWFyb1FOek9tNCtBT2dDOFVGdGdRNVVidzB3aytaS3ozVHhwQU55c0o5cCt2NUJJbHQiLCJtYWMiOiI3NTVjMmRkNzk5N2M0YTQ2NzBiMGMxMmIzOTA0MTNmYzkwZWQxYmZmNzhkOGVlOWNmZDA0ZDNjNTMxZTZiMTE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-992680664\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1385737441 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1385737441\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-833421007 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:25:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjR3d25lOGdpNWVYQ09pNTJaN3JKelE9PSIsInZhbHVlIjoibVZLVXZBa0ZBZzR4ZmRJL0RERlAxeGxNODhlSGc2ZGp1MGw4cVFiRWhLT3Q4ZTVMWG1Qai8yVm56a0prZUF3eDV3MVRaaXdZc2hMa0RjT1htcDRqaDdXeFZGU0lIK3ZhTERSeWsxSVZVWkdQbGwwdWxRYWFBdnNScEt5c2wxSURqcStkcGlmbjhpei83SlhBcDNsNkdVN2FFSDArUUo5ZklEa010SGptcGRNRy9pc2xkdkJVeU0rSVZLeVRBMG9RVUVYVnZvNDlkTVd5QlBCY1VmK0s4ZFlFVEt1NHF4b1ZNK2ZOeG91OU1jME5RMHJIdVhBa281OVZvb2Nuc283bXBxcHorS0dZcms5bjVjK3p2TEZHQytwTEhqTHh2NUl4aExBRURqUHhjaUNkWDRTOUlUUm91dFk2R3dUVEd1M1hycUpSYkJOZFI5U0Z6S2N0UWVQNTMyZDJySWJUNC9oYm9nbVRhd1c5dlJTVnBQSzlKMmpoY0kxRVhMUWJleG9jNjNZTld5ck80ZnMwa0tVdjZKQXJWS2dMUDJRaTZMUjdnSVluMG0yQ1pYaUdOTm5pMWVHY2lOclV6VnNVbnFzOWFTSHNqWXdCQmM0OXl0RGZjeS83UGRMZFJCSlhRL0VGNU51VGU2UXRsWjJmOUxEWTQ2SHhJbzFIRm03WjBqR0MiLCJtYWMiOiJjMGRmYzI5OGRkYmM5YjZjZmQyOWJjN2YwZWE0YzNiOWQ5ZGE0MWY5MzA3ZDU1NzQ0M2Y4YTZjZmRjZDg0MjMxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:25:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlBUbkFHQkY3K0ZCYWg4eUhMMlVIVlE9PSIsInZhbHVlIjoiWjVMWXRQVG5MM2M4bmNGODRQYUZSRFluc1JvdkxZWU1QWUpQUThPQ1psN3oxbmpJeW84a2RwSk1MSUZ6SDdrdzgyZSttQW4zUHFDQjkvWElyb2MzdHN0NkVBZ0xuWnB3NzdYcllOR28xY21WL3ZkdGFPejRzTUM0Q2ZYY051Sjk5WHIvN1FxSzIzcjZQVnJDQlBjVkU4MGg3NHZTQ2NuZDV5UythWGUzK3RXTGtNRUFuaUVhWmk0eXNjZk1kKzdLQkhwaFRZeGVCQ01vSnZ1VjlUMmUrbjEwTHU5cm1jZm0xQUQ5WlpET0M1bWx5VkZBekI4U0N1d1BVdWJoVk1mQWhpRktoUHpva1hVRXFFZHdyL0ZMUjdId2s3UHBUQ3NqcDVxQTFyRzhjbCtqb0RTNXZxbGoxWWw3UUh1OVg3THc3Ky8wQVdUV2VOVEVtM0haL0hwNGYvQ3ppZkh1TVF6MjdVTzJVUzJMc1NmNWtoaHBpTHBNQlNwWmhIZGtJK0phZE1SMXJmTWQ0cU0zTzlxZ081WDFYRHpvSVBsL2JpVm5kdVJtZ2NLZy91SlluOVBMUk5OTW1GUGJMZ2FlR0UxR3VOSjRzUXlzWmptMGtvc1o3Rk5lZzZIemtCT0JiclZiTEl5UmVpeFIwYmRubldxQTIxc25uU2REaXRVaWRTSDMiLCJtYWMiOiIxNjg2NjZkMjE5NWNlNmQzZTM1NGRjNzViODJiZWIxZjUzZjJkNGI5ZTg1YTNkZTE0MjU0NDg5MzZmZTNmNjBjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:25:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjR3d25lOGdpNWVYQ09pNTJaN3JKelE9PSIsInZhbHVlIjoibVZLVXZBa0ZBZzR4ZmRJL0RERlAxeGxNODhlSGc2ZGp1MGw4cVFiRWhLT3Q4ZTVMWG1Qai8yVm56a0prZUF3eDV3MVRaaXdZc2hMa0RjT1htcDRqaDdXeFZGU0lIK3ZhTERSeWsxSVZVWkdQbGwwdWxRYWFBdnNScEt5c2wxSURqcStkcGlmbjhpei83SlhBcDNsNkdVN2FFSDArUUo5ZklEa010SGptcGRNRy9pc2xkdkJVeU0rSVZLeVRBMG9RVUVYVnZvNDlkTVd5QlBCY1VmK0s4ZFlFVEt1NHF4b1ZNK2ZOeG91OU1jME5RMHJIdVhBa281OVZvb2Nuc283bXBxcHorS0dZcms5bjVjK3p2TEZHQytwTEhqTHh2NUl4aExBRURqUHhjaUNkWDRTOUlUUm91dFk2R3dUVEd1M1hycUpSYkJOZFI5U0Z6S2N0UWVQNTMyZDJySWJUNC9oYm9nbVRhd1c5dlJTVnBQSzlKMmpoY0kxRVhMUWJleG9jNjNZTld5ck80ZnMwa0tVdjZKQXJWS2dMUDJRaTZMUjdnSVluMG0yQ1pYaUdOTm5pMWVHY2lOclV6VnNVbnFzOWFTSHNqWXdCQmM0OXl0RGZjeS83UGRMZFJCSlhRL0VGNU51VGU2UXRsWjJmOUxEWTQ2SHhJbzFIRm03WjBqR0MiLCJtYWMiOiJjMGRmYzI5OGRkYmM5YjZjZmQyOWJjN2YwZWE0YzNiOWQ5ZGE0MWY5MzA3ZDU1NzQ0M2Y4YTZjZmRjZDg0MjMxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:25:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlBUbkFHQkY3K0ZCYWg4eUhMMlVIVlE9PSIsInZhbHVlIjoiWjVMWXRQVG5MM2M4bmNGODRQYUZSRFluc1JvdkxZWU1QWUpQUThPQ1psN3oxbmpJeW84a2RwSk1MSUZ6SDdrdzgyZSttQW4zUHFDQjkvWElyb2MzdHN0NkVBZ0xuWnB3NzdYcllOR28xY21WL3ZkdGFPejRzTUM0Q2ZYY051Sjk5WHIvN1FxSzIzcjZQVnJDQlBjVkU4MGg3NHZTQ2NuZDV5UythWGUzK3RXTGtNRUFuaUVhWmk0eXNjZk1kKzdLQkhwaFRZeGVCQ01vSnZ1VjlUMmUrbjEwTHU5cm1jZm0xQUQ5WlpET0M1bWx5VkZBekI4U0N1d1BVdWJoVk1mQWhpRktoUHpva1hVRXFFZHdyL0ZMUjdId2s3UHBUQ3NqcDVxQTFyRzhjbCtqb0RTNXZxbGoxWWw3UUh1OVg3THc3Ky8wQVdUV2VOVEVtM0haL0hwNGYvQ3ppZkh1TVF6MjdVTzJVUzJMc1NmNWtoaHBpTHBNQlNwWmhIZGtJK0phZE1SMXJmTWQ0cU0zTzlxZ081WDFYRHpvSVBsL2JpVm5kdVJtZ2NLZy91SlluOVBMUk5OTW1GUGJMZ2FlR0UxR3VOSjRzUXlzWmptMGtvc1o3Rk5lZzZIemtCT0JiclZiTEl5UmVpeFIwYmRubldxQTIxc25uU2REaXRVaWRTSDMiLCJtYWMiOiIxNjg2NjZkMjE5NWNlNmQzZTM1NGRjNzViODJiZWIxZjUzZjJkNGI5ZTg1YTNkZTE0MjU0NDg5MzZmZTNmNjBjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:25:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833421007\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-154388261 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154388261\", {\"maxDepth\":0})</script>\n"}}