{"__meta": {"id": "X4820bcc9639e4aab2627ec3e33ebd64e", "datetime": "2025-07-30 09:35:29", "utime": **********.250249, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753868128.43576, "end": **********.25028, "duration": 0.8145198822021484, "duration_str": "815ms", "measures": [{"label": "Booting", "start": 1753868128.43576, "relative_start": 0, "end": **********.162074, "relative_end": **********.162074, "duration": 0.7263140678405762, "duration_str": "726ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.162092, "relative_start": 0.****************, "end": **********.250283, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "88.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tJIa2rHgdqsvsBjBbkJvw1Cc4dCLv3bXnK2fQUIv", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1262591320 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1262591320\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-513111231 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-513111231\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1744669297 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1744669297\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-644010622 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644010622\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1916329256 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1916329256\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1007056784 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:35:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllUUFRNajZxcmlKaEl5TlptMjdhR0E9PSIsInZhbHVlIjoiWWhmVW05ZVBlT1ZCLysvdFVqU0dGZ0VveldFV0ptMFRaVHZqMzBMM0NTWkdPQXh3UGJkYXZacjY2TWIranVLMWZrZUxiaFRkUmhDbHVOSU1aVjBRTitaSzdFQ1dxZjN4ajdoeWFvL1VrZk0xWityOWdlamM1L2QrbmJLWGtjUmorTWVHWnZpMHZPTHgyRHhKZ0lGVitVNjR0b0hUTG15Z3hJTXdVd1hIcXF5bTJITjFkYk5aWVNlRmpXdWZSeGVJK3FQY1VuR0krZGZ0elU3TFBDMWhmVkx5UCtyUS9jUEc0V2dTYXpaQkpEOXVTNmptbHdWd1FuME10aDlUQU8yK3JPSzMwYit6eFdhNXo5ZDBZdnlrZWJVbFFJOE01MzRlK0pOKzJxV2RQWFQ3OVllOHVaYVdmdjBTRDVIb01VTE4zdmVBbG5QOUdMS2tBM2dRSUFuZEYvaC9oWVZyL2dFSTJLOFE2TUhWbkxuc295dU9hN0lVK1puZFYyYjNLRjNqRkx4UHp0cE5Rd1JQcFV1OTJkeXFXc1ZGb2pIMG9WMldCZEtXeTR0REdPblRIWFdBQnV5QmhlQzdTaTcxZnVIK0pDNldwNkpwd3lzNHZvbjR4cVJuczdNRUtYelJvOWRYRXhWS1hESVBzNG5rdEU3dzNOdUkrYkZONDJVd1RVc1UiLCJtYWMiOiIzYmY3ZDhiYzlmMDFmNGE4MmNkMjBlOGRlNGFlZWM2NjMzZGM5NDc1OTNkNDIxNjNlOWU2YjU5NTAxMmY4ZDQyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:35:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InVDWGF4NmJYOVJTeDFwVzVjYnFWZHc9PSIsInZhbHVlIjoiV3hXWGNZSEo0Y09EUW1yZzZ3VGFtQkJ0WHpSRFFLSUUyM05MLzZUZXp3ejdnTVg4TDVBM2t6Yi92UWNBUWNsL0tGQnFCOVJGMEk2UjdKdGprbTk5VzhUeSsrZmpLakdmOWhzbHpaakptK2l2WUVEYjVWbll2c2JhbGhtWGRmMjZkcXpYNjNIOTR0UVNZanp1U2dCQ3J6Y0svNWFlbFpCTUc2bExWSkI5NGRzK0c2Q0xhb1NHKzFmOWlxSGI0alhBUkRjTFhXNGY1RVhNQXVqZUMzWU5BNGFWejlyYlFydC84QzVoLzUxamo2WnAybi9rQ1I3dE95Y3dJSzI3VE4rL2pBdjNJTVRNRGtGWG4rVjZkSGJyaXRrbHBsbDJwUnl5RmsxS3NXUlM4L0ZJa2VQeHhqN0sxckNEbGZXcGR2dFdldTJBSXpqYTF2UEw0SU9RL0xjOTA5cEpRQkJmL04ybGkyNld0TlRzYXpobzlXbEpFMmNxQ2pVU1VtUm1ZbGYrNGVPUXpreWJnUmo3K1dIWHlXbS8xamp4eTJHLzk5d1VoSUtMMFlkWTVYc2hqRGdhRjEyc05lN0RIcE95UU1CVm9LREhBdnpLWG5lTG1QdlliWDBwc1NSRDYxT2gvMDkrb200VXhINTk5eG1IZXFRNnFLcndLNGIzbDN0MlNFQSsiLCJtYWMiOiJmYTljMjE1ODM1NWIzNjAyOTk4YzYzY2UxNDFjYTMzYmFjMmRiNGJkMjEwMmIzYmUxYWJlYThkMGUwYTVjNjMwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:35:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllUUFRNajZxcmlKaEl5TlptMjdhR0E9PSIsInZhbHVlIjoiWWhmVW05ZVBlT1ZCLysvdFVqU0dGZ0VveldFV0ptMFRaVHZqMzBMM0NTWkdPQXh3UGJkYXZacjY2TWIranVLMWZrZUxiaFRkUmhDbHVOSU1aVjBRTitaSzdFQ1dxZjN4ajdoeWFvL1VrZk0xWityOWdlamM1L2QrbmJLWGtjUmorTWVHWnZpMHZPTHgyRHhKZ0lGVitVNjR0b0hUTG15Z3hJTXdVd1hIcXF5bTJITjFkYk5aWVNlRmpXdWZSeGVJK3FQY1VuR0krZGZ0elU3TFBDMWhmVkx5UCtyUS9jUEc0V2dTYXpaQkpEOXVTNmptbHdWd1FuME10aDlUQU8yK3JPSzMwYit6eFdhNXo5ZDBZdnlrZWJVbFFJOE01MzRlK0pOKzJxV2RQWFQ3OVllOHVaYVdmdjBTRDVIb01VTE4zdmVBbG5QOUdMS2tBM2dRSUFuZEYvaC9oWVZyL2dFSTJLOFE2TUhWbkxuc295dU9hN0lVK1puZFYyYjNLRjNqRkx4UHp0cE5Rd1JQcFV1OTJkeXFXc1ZGb2pIMG9WMldCZEtXeTR0REdPblRIWFdBQnV5QmhlQzdTaTcxZnVIK0pDNldwNkpwd3lzNHZvbjR4cVJuczdNRUtYelJvOWRYRXhWS1hESVBzNG5rdEU3dzNOdUkrYkZONDJVd1RVc1UiLCJtYWMiOiIzYmY3ZDhiYzlmMDFmNGE4MmNkMjBlOGRlNGFlZWM2NjMzZGM5NDc1OTNkNDIxNjNlOWU2YjU5NTAxMmY4ZDQyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:35:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InVDWGF4NmJYOVJTeDFwVzVjYnFWZHc9PSIsInZhbHVlIjoiV3hXWGNZSEo0Y09EUW1yZzZ3VGFtQkJ0WHpSRFFLSUUyM05MLzZUZXp3ejdnTVg4TDVBM2t6Yi92UWNBUWNsL0tGQnFCOVJGMEk2UjdKdGprbTk5VzhUeSsrZmpLakdmOWhzbHpaakptK2l2WUVEYjVWbll2c2JhbGhtWGRmMjZkcXpYNjNIOTR0UVNZanp1U2dCQ3J6Y0svNWFlbFpCTUc2bExWSkI5NGRzK0c2Q0xhb1NHKzFmOWlxSGI0alhBUkRjTFhXNGY1RVhNQXVqZUMzWU5BNGFWejlyYlFydC84QzVoLzUxamo2WnAybi9rQ1I3dE95Y3dJSzI3VE4rL2pBdjNJTVRNRGtGWG4rVjZkSGJyaXRrbHBsbDJwUnl5RmsxS3NXUlM4L0ZJa2VQeHhqN0sxckNEbGZXcGR2dFdldTJBSXpqYTF2UEw0SU9RL0xjOTA5cEpRQkJmL04ybGkyNld0TlRzYXpobzlXbEpFMmNxQ2pVU1VtUm1ZbGYrNGVPUXpreWJnUmo3K1dIWHlXbS8xamp4eTJHLzk5d1VoSUtMMFlkWTVYc2hqRGdhRjEyc05lN0RIcE95UU1CVm9LREhBdnpLWG5lTG1QdlliWDBwc1NSRDYxT2gvMDkrb200VXhINTk5eG1IZXFRNnFLcndLNGIzbDN0MlNFQSsiLCJtYWMiOiJmYTljMjE1ODM1NWIzNjAyOTk4YzYzY2UxNDFjYTMzYmFjMmRiNGJkMjEwMmIzYmUxYWJlYThkMGUwYTVjNjMwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:35:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007056784\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-225867739 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tJIa2rHgdqsvsBjBbkJvw1Cc4dCLv3bXnK2fQUIv</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-225867739\", {\"maxDepth\":0})</script>\n"}}