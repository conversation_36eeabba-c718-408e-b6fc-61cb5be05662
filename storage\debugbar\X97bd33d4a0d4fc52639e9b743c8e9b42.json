{"__meta": {"id": "X97bd33d4a0d4fc52639e9b743c8e9b42", "datetime": "2025-07-30 09:31:57", "utime": **********.039186, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753867914.510808, "end": **********.039239, "duration": 2.528430938720703, "duration_str": "2.53s", "measures": [{"label": "Booting", "start": 1753867914.510808, "relative_start": 0, "end": **********.772609, "relative_end": **********.772609, "duration": 2.26180100440979, "duration_str": "2.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.772646, "relative_start": 2.261837959289551, "end": **********.039259, "relative_end": 2.002716064453125e-05, "duration": 0.2666130065917969, "duration_str": "267ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46674720, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1023\" onclick=\"\">app/Http/Controllers/FinanceController.php:1023-1082</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00666, "accumulated_duration_str": "6.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.975121, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 63.363}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.00386, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 63.363, "width_percent": 22.372}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1049}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.01679, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1049", "source": "app/Http/Controllers/FinanceController.php:1049", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1049", "ajax": false, "filename": "FinanceController.php", "line": "1049"}, "connection": "radhe_same", "start_percent": 85.736, "width_percent": 14.264}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-468560679 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-468560679\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1210591631 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1210591631\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-586639914 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-586639914\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1337363431 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjlhQmVyTDZ3K2VhTElQNjFxaW15THc9PSIsInZhbHVlIjoiVXRWOURCSUxJVWlPU2ZWamlPUTZYQms2UUd5aUsyT2NOTHU1eTZrYnNjSnFJTk9KV2lSRzNUdGIraldTOHV1ejhUQ1lpUVErMGxQUlpDWUNWdkJINFdxamthVGk5Mm9zVzNsWUoyczZHMmhzZzV2OFBNUkVXVzNzUnpPMkkwTHpOQUwxbkx4Tk9RNExLUkxiVUNZYTRGL1VYQWVjTDJ2MmMwdHVHTkhpYWV1dytUdjJ2ZWNxck1FUStNZS9iR1dVcXA2OTBGTjJiSW5ucjZpY3N4cVpNdzMzM0FFRTFMSkg0MDB5NGxLRVkvSU5IK3UzcVFNSkc3aWE2UE53d1d1NVpSeGZCbmh1U1AxNDlDWEw5S001eHdyY2JWTnFuQlI1a0VpcFNKRm5DZ2pBbm02eWR1SEtSeDh0THVBZ3FrVDFmU1QrUGplUVRMWUZ5c3YzV2QwZHFCQUwwL2pEOVVQRE0xalIwTUp3NGZ6ZnJBVnpxYTlxQmlwRXdpVTA0TFg4aEF6UTVkb3JJZVA1VjU5bDRiUFdHZ2lLZmxjNjA2Uy9wTEtIOTJ0cmpWUHdLK1F0UFh5RTNjbUVGL01DajluV1hoQllpN0FybnhUWEY5Um1GNEJSUksyMk43NzQ2NVAxUjJJVXNaUnNzT2ZIeDRXRGNjQ2JpSDVCVlVzaG93R2kiLCJtYWMiOiI1NTVhZTdmZTUzNmU0MWJiNjMzOWVkMDU0MDU3ZGMzNWUzNWUxYzUyZjAwMTI1NmQyN2FlNWVjZWExNTQxMzk2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkJhTjdLWFp4Y1dDUDFsUUFyczc1Y3c9PSIsInZhbHVlIjoiMHZPbkFRMkZTaHlNVGF6dHJHRWZNbm9xc3Z6R0pWQVRNcjlSMDdKa2R4RVFPVllVeldhTGhNMnJ2ZHY1RVFlallCY2llWk4vOUd2akd5REdLelVjZFM2emVqY2t2TWJZU3ppQkh6ME1hUFNBSUdhWFBkRkdNOGViWEFTckhrWTY5VDJMQndpRnRHZ1hHZDByZ1NpRW9zMEVBRGNHQ0JLV0QxdEVWaVN0Q3hhZlBLVmFHTVVlMnRLUGtkNmx3Z0pJZXZCbUJRNEcvWG1WbmpCc0lYTE82VFNlZ2JCNVNiZURRdlVma29HdHkzbU9xVUFEaFVsSnRxREl6b21ZYjJZR2gwamR1bktNMnM0MzRCb2YrdEQ4Q3l3V2ZFNEl4NU4rQlFMZk13U3lBb3FNTGRWc3ZmQlJNSXNLT2sxWVloR3hTVWJHSC8rckRqUVpMdWM1dVJJVm5ZMlQ0c2pXbXFZSE9SN3NETkQvZzlyRS9TZStRUVVJZWFTYWdTb2lrcVlvMnZRVWhVcVZackkzWCt5QmM2NXAySUp2VXVTSlhWUjk4QjBZbVN2U05jOTBUS3ZPbXk0Smt3Z1NDeDA3QU0zZzg3NTUzTWZBRVZ5VmMyRWpIMTd6V2RHcGt4T2NQN0hKQTBpMkFZZTBzV3M3b1gzbWo3WHdIcWx2c1ZaYnZIRkwiLCJtYWMiOiJkMzZhNDU4NTZlMDA1M2Q2NTZlYzlkZTUzZmJkODZhMTM2MzVmN2UyMGM3NDlhMGUxMzQzMGJkYTkyMWQwMDZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337363431\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1267321879 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267321879\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-214560608 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:31:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InA3OHl3YUdYeDVUd0hvT3lyRkoxa0E9PSIsInZhbHVlIjoidkw0T01mYThYa2l4TTY4eUcwWDJMNUt1T2tNNHh0eVVOcnB3SkIrRUZvMFBPak0wNktlOXVYQmFmZWNwbjlTVUNBSjNqTmU5YVZ3SVcvbjZMdlNvMGlFN0wySm51aVJnd1JWQjhxa3Q3b1VzM21ycXpCM3ZkQ2FaVE1xVmRENmVZOEJ6azlxcVY0RVJVeW5ETXFyZzZ5VlkvWFdVQVd2OEJtalE4aThuc1lsNXI5cVRSWkQzNnNucVpWTThrdy9oUiszTXhUdWhPckM5NUwrYnJJWlhBMGVHdzIvMHcrRFdNczY1azVMOXNBVS9Gc0FIU0x2SDJxRmRTcDhFZ0RDNUhaVFA0cVUzcE9SeDdjVnpLMDFhekhxWUNQbzZtS3FsWEFPK3pBVXY5L3dVUDFRZWZiZUVZVmxOMG85WlAyMy9vTHBsYkRjRWY3aTRBblFGR2VoNDdNYU9yTUE2SlA2eHFnVDNma2tVRUV1NlR1dERMR2FSQWljRFh6RnBzbDY0bXNXdnJHcHBqRW9Bbjk4cnB5TUdSUUlXeWZuVmJLVDE3dzFyYTBXekgyaGJvU1FNOXZyM1cwVmhQYkpjZEs4SFV0NXYxZFhVc1JhRURBUkwwNU9oRUVxUUthOG9DNXBwQkNabFpiUTgrODlPckhLVm0vODBSQ20wQUxxMmM2RzciLCJtYWMiOiJhMDVlYWYwZGRhNjJkYzI4MDEzMDUwNTRjNzg0MTkxYWNjMThkNDFlYzExNGFkY2ZhYzY4M2JjNjNmMWYzMDY1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:31:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVRTHNMaENneWdVQ1FXRDMzKzNodWc9PSIsInZhbHVlIjoibWt5dlZzWFJCYWF6UEFrUEF4MDJjcDNXT0tMWjB6NTRsVjJnamcrdXJRenRONEpMWjJpQ3gzMk1tVVFLbjlnU1VVS3phTlBSekFsb21PbEVaS2ZkYlZSRFJLVmh5bFNhV2xhTkdPd3JlaWUzWEQ1cmhZQnAwZjBXajRsVTd0N1h6K1UrWGlVR3VxREdDWk9JNklMS2hrM0NCYitMMGJQbUZtbldTOTNYOWRORURMaUtvSkhDQWFSdXFOYjZDOE1BZGZwdSthZzcxU1BsSFBsemhyWjRJbng2T3VhZ0lWZjRZVDRENTQ4U0ZRMEN6bDllcllSd0w5NDV5QWZXQjVGU0I2MnR4WWl6Lzhhd2s0UzNpbXMyL0pIWkF0b1hkcTR3NjRFbUlWaGJlcitKd2x1d0ZwVU5NMTdQSjkxc29nQWw3eWl1eWFiUkVWZDZWWkl6YXYzK1JqVGkzOHhZV3hsOEhiSnRhcmVBTFpxNUxNQXdsclFVa05LODh0U3JtZmc2c3lCRWxDSERtSjJ4SDZiZFc5Nyt5VE5CdTNyQWFaeEhHZ0lWMk9OQ1ZRaElrajd0Y0dUUHkybFRnMUNzRmw0MmRsZW9lZEJpM3BiMnFrQ3gxaEpkQ3BVR01qOTE4SldGYUl6cHJMQkc4Sit6L3NZVnFGVEs2aCtRWXA4S0VpcU0iLCJtYWMiOiIzZmI4MGRhNmI0MDA1N2IyYTkzZmJiNzgzNDZlNzMxZDM4OTdmYmNlNDU1NzVhOTM5ZTZmMmI3NjQ4ZmMzNGI3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:31:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InA3OHl3YUdYeDVUd0hvT3lyRkoxa0E9PSIsInZhbHVlIjoidkw0T01mYThYa2l4TTY4eUcwWDJMNUt1T2tNNHh0eVVOcnB3SkIrRUZvMFBPak0wNktlOXVYQmFmZWNwbjlTVUNBSjNqTmU5YVZ3SVcvbjZMdlNvMGlFN0wySm51aVJnd1JWQjhxa3Q3b1VzM21ycXpCM3ZkQ2FaVE1xVmRENmVZOEJ6azlxcVY0RVJVeW5ETXFyZzZ5VlkvWFdVQVd2OEJtalE4aThuc1lsNXI5cVRSWkQzNnNucVpWTThrdy9oUiszTXhUdWhPckM5NUwrYnJJWlhBMGVHdzIvMHcrRFdNczY1azVMOXNBVS9Gc0FIU0x2SDJxRmRTcDhFZ0RDNUhaVFA0cVUzcE9SeDdjVnpLMDFhekhxWUNQbzZtS3FsWEFPK3pBVXY5L3dVUDFRZWZiZUVZVmxOMG85WlAyMy9vTHBsYkRjRWY3aTRBblFGR2VoNDdNYU9yTUE2SlA2eHFnVDNma2tVRUV1NlR1dERMR2FSQWljRFh6RnBzbDY0bXNXdnJHcHBqRW9Bbjk4cnB5TUdSUUlXeWZuVmJLVDE3dzFyYTBXekgyaGJvU1FNOXZyM1cwVmhQYkpjZEs4SFV0NXYxZFhVc1JhRURBUkwwNU9oRUVxUUthOG9DNXBwQkNabFpiUTgrODlPckhLVm0vODBSQ20wQUxxMmM2RzciLCJtYWMiOiJhMDVlYWYwZGRhNjJkYzI4MDEzMDUwNTRjNzg0MTkxYWNjMThkNDFlYzExNGFkY2ZhYzY4M2JjNjNmMWYzMDY1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:31:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVRTHNMaENneWdVQ1FXRDMzKzNodWc9PSIsInZhbHVlIjoibWt5dlZzWFJCYWF6UEFrUEF4MDJjcDNXT0tMWjB6NTRsVjJnamcrdXJRenRONEpMWjJpQ3gzMk1tVVFLbjlnU1VVS3phTlBSekFsb21PbEVaS2ZkYlZSRFJLVmh5bFNhV2xhTkdPd3JlaWUzWEQ1cmhZQnAwZjBXajRsVTd0N1h6K1UrWGlVR3VxREdDWk9JNklMS2hrM0NCYitMMGJQbUZtbldTOTNYOWRORURMaUtvSkhDQWFSdXFOYjZDOE1BZGZwdSthZzcxU1BsSFBsemhyWjRJbng2T3VhZ0lWZjRZVDRENTQ4U0ZRMEN6bDllcllSd0w5NDV5QWZXQjVGU0I2MnR4WWl6Lzhhd2s0UzNpbXMyL0pIWkF0b1hkcTR3NjRFbUlWaGJlcitKd2x1d0ZwVU5NMTdQSjkxc29nQWw3eWl1eWFiUkVWZDZWWkl6YXYzK1JqVGkzOHhZV3hsOEhiSnRhcmVBTFpxNUxNQXdsclFVa05LODh0U3JtZmc2c3lCRWxDSERtSjJ4SDZiZFc5Nyt5VE5CdTNyQWFaeEhHZ0lWMk9OQ1ZRaElrajd0Y0dUUHkybFRnMUNzRmw0MmRsZW9lZEJpM3BiMnFrQ3gxaEpkQ3BVR01qOTE4SldGYUl6cHJMQkc4Sit6L3NZVnFGVEs2aCtRWXA4S0VpcU0iLCJtYWMiOiIzZmI4MGRhNmI0MDA1N2IyYTkzZmJiNzgzNDZlNzMxZDM4OTdmYmNlNDU1NzVhOTM5ZTZmMmI3NjQ4ZmMzNGI3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:31:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214560608\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-393243770 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-393243770\", {\"maxDepth\":0})</script>\n"}}