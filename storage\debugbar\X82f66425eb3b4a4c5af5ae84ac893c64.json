{"__meta": {"id": "X82f66425eb3b4a4c5af5ae84ac893c64", "datetime": "2025-07-30 09:11:39", "utime": **********.981242, "method": "GET", "uri": "/finance/sales/contacts/lead/12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.214753, "end": **********.981265, "duration": 0.7665121555328369, "duration_str": "767ms", "measures": [{"label": "Booting", "start": **********.214753, "relative_start": 0, "end": **********.865345, "relative_end": **********.865345, "duration": 0.6505920886993408, "duration_str": "651ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.865361, "relative_start": 0.6506080627441406, "end": **********.981267, "relative_end": 1.9073486328125e-06, "duration": 0.1159060001373291, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46686760, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=977\" onclick=\"\">app/Http/Controllers/FinanceController.php:977-1036</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028020000000000003, "accumulated_duration_str": "28.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.922277, "duration": 0.02639, "duration_str": "26.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 94.183}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.964029, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 94.183, "width_percent": 3.212}, {"sql": "select * from `leads` where `id` = '12' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["12", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1003}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.969276, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1003", "source": "app/Http/Controllers/FinanceController.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1003", "ajax": false, "filename": "FinanceController.php", "line": "1003"}, "connection": "radhe_same", "start_percent": 97.395, "width_percent": 2.605}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/12", "status_code": "<pre class=sf-dump id=sf-dump-1819704795 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1819704795\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1945081414 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1945081414\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1038803770 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1038803770\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1332674599 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IldYMkJJS0NSY01jUk5vQWljaUVqY2c9PSIsInZhbHVlIjoiTVNiYjVYNkZtdnJ3aWdiTFR4aWNmazFQeGZRTzlGM1Q2TnRCZ3Z0K0ptK1BkZmFvY2N2OVlPZWxMSmYyaHpEUlk4UGJrazRmSWZiWXdJNU5nSnRXckV1K3hWTlg2M0I4cE1oYVVuTVp2OTBVYi8vSWQ3T0U1SjE3MDZCQVZONlRBemMzYmlPc0NxcGNwdHZTTHRIeHR6YU5VdHJQR2lXT0ExQ2VrVjhRc1ZiUWQ5SDAxU0c0QWRtMTk3WlpJeUdraDVNKzNwWG02SnRsQTZ3MTVJQUJsbW8rM3ZuTFJib3pYRE8yM3ZPeUkvVXpBb3d2M1F4bWdmVE51MS9kaHJuVUlod0xMTDFZQ2UrajBQSWpoTEJsUzJkcXRVTjdRdXZ0dXpCL0ZvTHNkMUNlUVBRVlUwL05xL2JFakR1Qy9aMjFnM0IwdWs4UnRPNk1UY1NWZXRUcHc4ajBRKzg3clNFVTMvUVhPekxrWmdpVDNuVVpTTEVnbkNRTVVmMmV6Rm1QZWpMNytOQWJFQlVTUUVNMVdIN3NKckxKRThQK0tVVW1IQkliQ2VQM2RGT2dNaDBHRUtXWjBhenZzYXlCRjNWNXU4azFFS2VEbGFaTys2Q3dqbHY2OXZva1p2bDQxOCtiVUt5c1JIYjIwYW9QWmJ1WHBnekhoUERkTGRDaXpMTWwiLCJtYWMiOiIwYmZmNzMyZDVkNjk2ZjczNDdmZjIwYjIwM2U4MjI3NTkxYzc5MzgyYzIwODI4YWEzZGVkYTU2ZDg2YzVkNjAwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImR1MEVQK1FjUVRxYkhLVDZBRXpkZ0E9PSIsInZhbHVlIjoiVTZ6bEhDU0ZEbEk2YnlzbnNtekhXZ1pxbXpzcG03M2FSRytNSXg5SFNOWHA0RE9NWFdLL01QRmdRS3RHK281K1FqdFBJMzBTeEJTaGVQWVk0OTFJQmtMWTdGN0VrWHFvZWZqWnZqdWRiQWF2Zk5zK3UyTCtRL3NVRzVqaTI5TXhtVHVwWWJQZWxrZkR0NHZLdjBnb3JhSW85WVR2bXZVMElLV0grc1NiMVV5bjZUUkFJNTF6YTluVVV6VWFNbWREWE5MV3Q2STVPRkJYY1RKU3JjK2xmT3JndUsrSjBMT0lQUE92S01xK1RnVkcxVE9sR292T1ZtRlV5NkRoMDdBMVJXbkcycTU3WkZBQyszZmswZG9iaHBVUW94eFFqWmRNd3QwUnVDUzdUZW9LN1paeE8zR2NoRnZ6NW1XcjBzV0ZRRGZrWFRzc25QeldCZFZWQVp6dzZXUk5aZ2hWeUVGbWNJVDREME1uZzJKSDgwQTZ3UzNQMVF1NytvWnc0LzFlWm9rNXcxU0w0SUpOMStOYytVN2Qyd3FTYktHZHhKUUJzTmVlbmc2UFVZdDMxUHVGYzhsZGtIMUo3SUt4bFdFZExLZHE5eUg4ck8zeURqTlNuTWZqdGNrc0VFcWFQK0NYdUl2ZzNnT29NNEhJQ25NR0R0OUVHV1FJSmZ1UFcvMkoiLCJtYWMiOiJiNjAwMGIzMTNmNDkyOWJjZDVjMzRlNThlZGM4OTRlOGM3ZTFhZjY3YWZkYjBmZTM0OTMzMGYwYjE4N2YyYTgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332674599\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1222441252 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222441252\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:11:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9xN21Lc2NHYjFvUTRERUtGSmxxc0E9PSIsInZhbHVlIjoieThTN3MxcTdNRm00dE02Y3l6SjA1bE1JQ3RLeEFDZC9oSGVKVUh0V1JWbzFZdDdZZVRoR3JGWjQyS2doTFAzUFZtbE8vL05vcTFoZWxWemorNjFuQXFxb3VIdHVoYys5VVJKdUV1T0tPdG1zRXdOVkFuS051YUJwUnRNRlpiV2U3Z2tCYWtLVE1GSy9TdWxRYjRPUkR1ZUt6ZmVlQXh2L29RSndUNy96bnhrVzJTaDRKTTkwaGlTTWJrM0V4Ri9wYUwrL2FYYnZFcXZhL0tzczlhSUVIdjVWZGg3a1ZJUGI5NHovWUptN2lWdFNtazVJNTBpMHpQNHd0YXlVeW9kQjBSRVdoUFZzalQ3RHVVM00yZDBBUVU0ellDREFvc3pSb3Vxa1FKQnVyY1NIYVUvQ1U1aEhLNnhURTJlTUFTUENIMlVNbUt6dDBNazR1K1lhN01rSE9zWDdVV3dVQThqTmxJSU5LUlpZSkd6eHNqSEw3UFYzYTdEVnlHQ01idytmV1pRcUdKUkNWVFhaQnJWV2w2WnB1OTBjVW9BUmRYeDJvWmJiZkdiZStyVWJVQ2ZvNEFyeXI2NHVQcmNyL084MFVBR3BzVjE1ZUxwdW5pUVhZRVRNeUlJaWlTSXZlcHg3a2ltZDBORURZOXNVenUyN1NUeDArM3hxUnNINVJCZ0kiLCJtYWMiOiJkM2EwMmEzNDQyOWJiMGE3NjYyMWJkYzNmZDhlNmEyOWEyNzUzMTIyMTJkZjE1N2Q0ZmU2MmZhZWNkZGJhY2FhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:11:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikovc3kwNm5GQTBBU0JXcTV6YW4xU3c9PSIsInZhbHVlIjoiMWRoZkk1bGk0ZGtReGZ5YjljNHlMMzJPTTRWNEhlemZ6RGtYeGJIS2RNZzFjMHJxMzRUR2pDZVFYcVk0WHdrYWJIZlE4eWt6RlV1TFlHbWUwUS9TNTJQdVV5TFk1L3llYXVqdThLNEZhZUxISTYwdUM1UDdyNUVGVVN5R1M3ekVUUTlNT2xMOEU2YmtTcmdweVhCM00rQVNKUjZmbFdobGljMUxtejFHT2hMc0RxY0RkOExXRVhxdzFiM1FEVlZTd2VGa1BibVh5c1M2MUtocTd2STdzdGFPVFpYTEg2NjlmRnZmT0RmOVA4WEFpWUMvZTJIT3NUa09xT1ArbWs4VmthRVJhWjV5dEVDcWF1U05rYWdLUjVJY0JNRTZDOWlBZE9hVFRYWW5sZURZQndGTXVvZEFmZ1J6R1REZVVoRE05eGcrbEFMdkV0QkFIMFlvOVFCajJxZWQrNlNXSDZoai9nNWhGYWJZeHRzMGRMK2JoeHM0ZEw5KzlLY251RDFJOHVmVXc1WVJLaUcrWUNHQm5abExBODM5aE52YjI3WVJFVnZ6U3AvYjZHZFh3dTc3MkNPT2ErRkNIK1RJU3Q4WUpTOTAxZmhGKzIxdWQ2ZDYvOFVDU1o4ZGVYMTV2eUhSMkpJemVKc2syMzVRY1dMMko0Zy96My9jWTRFS0ljOEkiLCJtYWMiOiIzODVkMDA1YmI2ZTAzYjY2NWJmZjMwN2I5NzkyMDQ2NjU2ZDA4NDMxZDlmZGM2ODI0YWI0YmNiMmE5MzIwODkwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:11:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9xN21Lc2NHYjFvUTRERUtGSmxxc0E9PSIsInZhbHVlIjoieThTN3MxcTdNRm00dE02Y3l6SjA1bE1JQ3RLeEFDZC9oSGVKVUh0V1JWbzFZdDdZZVRoR3JGWjQyS2doTFAzUFZtbE8vL05vcTFoZWxWemorNjFuQXFxb3VIdHVoYys5VVJKdUV1T0tPdG1zRXdOVkFuS051YUJwUnRNRlpiV2U3Z2tCYWtLVE1GSy9TdWxRYjRPUkR1ZUt6ZmVlQXh2L29RSndUNy96bnhrVzJTaDRKTTkwaGlTTWJrM0V4Ri9wYUwrL2FYYnZFcXZhL0tzczlhSUVIdjVWZGg3a1ZJUGI5NHovWUptN2lWdFNtazVJNTBpMHpQNHd0YXlVeW9kQjBSRVdoUFZzalQ3RHVVM00yZDBBUVU0ellDREFvc3pSb3Vxa1FKQnVyY1NIYVUvQ1U1aEhLNnhURTJlTUFTUENIMlVNbUt6dDBNazR1K1lhN01rSE9zWDdVV3dVQThqTmxJSU5LUlpZSkd6eHNqSEw3UFYzYTdEVnlHQ01idytmV1pRcUdKUkNWVFhaQnJWV2w2WnB1OTBjVW9BUmRYeDJvWmJiZkdiZStyVWJVQ2ZvNEFyeXI2NHVQcmNyL084MFVBR3BzVjE1ZUxwdW5pUVhZRVRNeUlJaWlTSXZlcHg3a2ltZDBORURZOXNVenUyN1NUeDArM3hxUnNINVJCZ0kiLCJtYWMiOiJkM2EwMmEzNDQyOWJiMGE3NjYyMWJkYzNmZDhlNmEyOWEyNzUzMTIyMTJkZjE1N2Q0ZmU2MmZhZWNkZGJhY2FhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:11:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikovc3kwNm5GQTBBU0JXcTV6YW4xU3c9PSIsInZhbHVlIjoiMWRoZkk1bGk0ZGtReGZ5YjljNHlMMzJPTTRWNEhlemZ6RGtYeGJIS2RNZzFjMHJxMzRUR2pDZVFYcVk0WHdrYWJIZlE4eWt6RlV1TFlHbWUwUS9TNTJQdVV5TFk1L3llYXVqdThLNEZhZUxISTYwdUM1UDdyNUVGVVN5R1M3ekVUUTlNT2xMOEU2YmtTcmdweVhCM00rQVNKUjZmbFdobGljMUxtejFHT2hMc0RxY0RkOExXRVhxdzFiM1FEVlZTd2VGa1BibVh5c1M2MUtocTd2STdzdGFPVFpYTEg2NjlmRnZmT0RmOVA4WEFpWUMvZTJIT3NUa09xT1ArbWs4VmthRVJhWjV5dEVDcWF1U05rYWdLUjVJY0JNRTZDOWlBZE9hVFRYWW5sZURZQndGTXVvZEFmZ1J6R1REZVVoRE05eGcrbEFMdkV0QkFIMFlvOVFCajJxZWQrNlNXSDZoai9nNWhGYWJZeHRzMGRMK2JoeHM0ZEw5KzlLY251RDFJOHVmVXc1WVJLaUcrWUNHQm5abExBODM5aE52YjI3WVJFVnZ6U3AvYjZHZFh3dTc3MkNPT2ErRkNIK1RJU3Q4WUpTOTAxZmhGKzIxdWQ2ZDYvOFVDU1o4ZGVYMTV2eUhSMkpJemVKc2syMzVRY1dMMko0Zy96My9jWTRFS0ljOEkiLCJtYWMiOiIzODVkMDA1YmI2ZTAzYjY2NWJmZjMwN2I5NzkyMDQ2NjU2ZDA4NDMxZDlmZGM2ODI0YWI0YmNiMmE5MzIwODkwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:11:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2097499522 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097499522\", {\"maxDepth\":0})</script>\n"}}