{"__meta": {"id": "X9fd31b5dacb8e01ca48e4a9218ccbb33", "datetime": "2025-07-30 09:31:42", "utime": **********.320805, "method": "GET", "uri": "/finance/sales/products/search?search=Bo", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753867901.563073, "end": **********.32083, "duration": 0.7577571868896484, "duration_str": "758ms", "measures": [{"label": "Booting", "start": 1753867901.563073, "relative_start": 0, "end": **********.229329, "relative_end": **********.229329, "duration": 0.6662561893463135, "duration_str": "666ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.229345, "relative_start": 0.6662721633911133, "end": **********.320833, "relative_end": 2.86102294921875e-06, "duration": 0.09148788452148438, "duration_str": "91.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46676680, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/products/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchProducts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-946</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00829, "accumulated_duration_str": "8.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.285306, "duration": 0.00628, "duration_str": "6.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 75.754}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.303863, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 75.754, "width_percent": 15.078}, {"sql": "select `id`, `name`, `sale_price`, `sku`, `type` from `product_services` where `created_by` = 79 and (`name` LIKE '%Bo%' or `sku` LIKE '%Bo%') order by `name` asc limit 50", "type": "query", "params": [], "bindings": ["79", "%Bo%", "%Bo%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 923}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.309373, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:923", "source": "app/Http/Controllers/FinanceController.php:923", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=923", "ajax": false, "filename": "FinanceController.php", "line": "923"}, "connection": "radhe_same", "start_percent": 90.832, "width_percent": 9.168}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/products/search", "status_code": "<pre class=sf-dump id=sf-dump-1443731752 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1443731752\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-673025584 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">Bo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673025584\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1090980272 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1090980272\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1615296582 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlFLdWJoTFNwdGYwdXNIWlBtelBBbEE9PSIsInZhbHVlIjoiR3FSWDdMTVB1bHZ6WHdKU1VkTjliQW9DUGV3Y05RR2ZDajQwTXZZV3Qyd1NoMWJ4MVgzeTVrTzhUT3Q1bUxiVWU5bDViSzV1a0Q1Wno1T0ZFOWoxUGdYNXZLdUtObU5EK1JObUNxU2xpczVPKytJOWhMSXAva01PeXM2VXk0d0EvUWU4dko4eEJPNE1zRTBxZFd0eWxzbklmUWd2UmpvdGJQc2d3TlFnTDUxVVpuUEZvQlRZQ2JLK1VVeDJuTUp5cFI1dVRaeXkxeE5Gd1JRYzNQcXJ0WWRNWVNRWnBYQy9kcTgwNnU0a1htUGFTTjhCbGsxRHRIMk03S0pxcW91c0MrZ2w2Nk9nVFpvMlEvMEREdkxubndTRkhnYVJPRXV6a3llRUd6T2RRSUVOSVZPMng4c2YxUHhMREh1UUtDWktGVm5zdS9IQmQrQnV4LzM4SGd5VHU2MHYycFNmb2crVFdtclVEQXh4bS94SG53RWRicVE1eHVDUHJPalhmQkR2UDZwMy9tc1B0OGxHalROdm1tQ3VoamFlYjJxRmt6VUhRYkxsd09QT0p2ckM0MGd6WmhmT29zN0s3cFdMMUtpQ1l5T3JldThZdzczK3BrajlyUDBNT2RZcDFTWi83SE9hMFE3Umk3NnVBUHRGYlJrRVZxbXpMOFJqRThHR2s3TXoiLCJtYWMiOiJkNDViMTZiNjY3MTA1NjhmOGRlZTVmNjQwNTU1MzQ1MzRhZTkxZDg4YTc0ZTc1MWVlM2M0YmE1MjYzNzYxZDIxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InRTTzUwMmlDb1RaQm5pN1RqaStNcWc9PSIsInZhbHVlIjoic2xra3dWSXMxY01JSElqQ1d1azlJZHc3c1UxbWpkK2FGOG81VUZVUjJUUUljSFpJS0dIbUJUeFZGenYvSE9wZFE0SER6QzlxSFErdjR1S2tPak5IN3JaT1ZBY0FjK0JGczNKWklrNnJxT1FvUXdpMHNUalBDM09UQUVheENqdW9HcTJlNVByd1EwbW13WTUyL1lPaHJWeC9XUHZReXVlVzZORmNWalg2MmVGd1BOdXVCeUh3OVJ5MVdPZTF1SkNocCt0VXhGUGt2Sys1Vzd6OFVmV0ZxMG5GM1dWWmk4QzhtWHZuTThZVVJLendKc0xtU0NsZml0M2dLL1VQZWRjRUlJaUVRTXlUenN2UWh0aEVoOXpqVENyeUdGQ0Rmd0ZIYkZiUmRrL2ZML1NBSFVPVXdFeHIzM2hDVSszNng5QzdsdnZLNmo2c3VHa08xZzFHNHo0Z3ZqdFJIUFJGQ1RxMHFTTmhGeDJWbTVFU3RaR053ZWQzdGhIMXBxUGxNSHBTZWR6QVhQYWpGTzF3ODB6L21nWGxjS3l6UzYxNUphRS9EYndyalFvbW1aRzZZN2E3bHVLMmxuM2FhUTBybkNJeER3Z3NVWFJpUTVENzUzS1gyVXA1UStzS3RudnNTeXJBb1kvUGVBbUhEeXBNUUtLcy9zd1ZFUVJEVngxbWZTL0oiLCJtYWMiOiI1ZDVlZTg2ZTRkM2EzOGIwODJjMTg1MDMzYjJlN2YxMmRjMmFlZjA0ZTg3MWQ3N2Q1MDA3MTA1MmZhMTFjM2VmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615296582\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1482071875 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482071875\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1538465689 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:31:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNkWG9VMkRHTlVOTTNXU2JaZzFVNVE9PSIsInZhbHVlIjoiVkVBcnhuNXJmMWtkdjFVYWZCdEtKdW5vL1p4SFZsR0VNcURldjYyR0JYQXFDVERSckVFYTVRbHQ5QlNiRDlMWURjU1IzVUQzVGZSL1dHQVhqZkdTRjhWV1lQVFRhaHlPaXVvblE1K1d0Q2U5eWNOcGNnNURtSENaWi9qSUpLcFdDd2ljcDFDYlRvRnlvRlVUZnB1aUxTK2YxUW1xZHZlV2tQNElQaWE4empLS2RkOUlabTRFaTl1SUp5TThRcW8yK25ybHQzY3ZXN3ZwRDNWK2l0a1ppVGZsTnZRcG1lVXFibFZiZ3NVWXBRVjh3MGpaYkwyNk9iT1dBS1JMRGQwTUdva2ZVanlXOGxTWW5rUDJUczFtOEk2bllFMlQyY3E1VlQwaksyUHNNdjNhekNhbmZWR3NQVTA4TUxmVTMwZFQwYUpVNVBML0JTVlVicno1ZDNDd0liM3VGZUMvREt6OGJXM1JvQUwvQlZ1OXYrL3pDYUd4c0lGbnpnV203eExIL0ZRYXE5bEZTKzEvMmtQWnF5UVI0cklKYWErOUlMWEEzWEJmOGdKYlZ1eUhxQzlVeFV3Tk9SS3kzbldwSnJndkhkMmJZc002YjJQaEpOdWpNekhneEN6eVZ3S1pldGJKQ2ZwbTRFcGVNelNRTk9aTkgwd1FYSkRjaDZLMlhOS3AiLCJtYWMiOiI2ZGFkY2ZlYTI2ZDNiNzc2N2Y2ZjM0YWY1YTY2YjIxNmMwNTA3MGQ5NDkwNzFlZjgwZTFiYTRhNjZkODY4NjdkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:31:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IklsbnJqaXFFTU9IMHRhQnhGcHhXUUE9PSIsInZhbHVlIjoiZnNsdWh5djVSVHlFZVBaSXo4aEtQQUlVaDF5bDUvZTlHR2cwd2lzckNuVkVCTUNUT3hoQzBKZnN3T3ZFajRtV3NnN0hlRExDbVFYYnFzcU5NcmhScGx0RGJMd2U2WC9KZ3hqRVlPbVdWVzdyWm03TXJrUHZvU2xTMUR5d1p3dkNrSXUvSksxMkIxQ0tsVHJkNGlKbmFmSjY3SkhGOXM1L0hFYlFMMk5aVHNncU80NC8rNFduYnlvVGdodUVKeEEyU2VOUjJnRmJNVzJSZVRFNGtYam1LRi9iM2pmRGZMMFhzWFBIMDZ1VlpnWitBOUZqeTY5YzNCVENvbXJVbllnOUhaekFNN0orNEt0QWpETlFVaW5kdnZ1MG4rMlllMXNXMmpISGxSWDZRcW1LWWJWVWtUcGpkZ3FUZHF2cFZRTWxhUS90SHNXOEdOQmJpdFdSQ2JvNjZSL2k4cndrb3pKT2hCa1l3U2FxZFJMRFJGcTAxQjAvV1QyaFN5emJ1RVVHemp0QndTRmtvL3RtOHZDOWN1MTlMbTh0S3o2OThjWTljeWtMQXdOeG5DRHRvY3BWdHZFRDZzVGEvUlZRTzR2azlQOGZvZGtxWjVBQnpObzFMRFJGa2EzNE8zYlBGMWlLUWlPU2U4dzEyOFJVZWVUNW9yVG03bXRXa25iSzBFeDQiLCJtYWMiOiI4ZTNiZGRhMzc2MGQ1NWEwOTU2OGNhNWYxYjhmYzg2OTJkZWNkNjBkYmFmYjhkZWQ2ZTc3MzBhZDNmMDkzNWNmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:31:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNkWG9VMkRHTlVOTTNXU2JaZzFVNVE9PSIsInZhbHVlIjoiVkVBcnhuNXJmMWtkdjFVYWZCdEtKdW5vL1p4SFZsR0VNcURldjYyR0JYQXFDVERSckVFYTVRbHQ5QlNiRDlMWURjU1IzVUQzVGZSL1dHQVhqZkdTRjhWV1lQVFRhaHlPaXVvblE1K1d0Q2U5eWNOcGNnNURtSENaWi9qSUpLcFdDd2ljcDFDYlRvRnlvRlVUZnB1aUxTK2YxUW1xZHZlV2tQNElQaWE4empLS2RkOUlabTRFaTl1SUp5TThRcW8yK25ybHQzY3ZXN3ZwRDNWK2l0a1ppVGZsTnZRcG1lVXFibFZiZ3NVWXBRVjh3MGpaYkwyNk9iT1dBS1JMRGQwTUdva2ZVanlXOGxTWW5rUDJUczFtOEk2bllFMlQyY3E1VlQwaksyUHNNdjNhekNhbmZWR3NQVTA4TUxmVTMwZFQwYUpVNVBML0JTVlVicno1ZDNDd0liM3VGZUMvREt6OGJXM1JvQUwvQlZ1OXYrL3pDYUd4c0lGbnpnV203eExIL0ZRYXE5bEZTKzEvMmtQWnF5UVI0cklKYWErOUlMWEEzWEJmOGdKYlZ1eUhxQzlVeFV3Tk9SS3kzbldwSnJndkhkMmJZc002YjJQaEpOdWpNekhneEN6eVZ3S1pldGJKQ2ZwbTRFcGVNelNRTk9aTkgwd1FYSkRjaDZLMlhOS3AiLCJtYWMiOiI2ZGFkY2ZlYTI2ZDNiNzc2N2Y2ZjM0YWY1YTY2YjIxNmMwNTA3MGQ5NDkwNzFlZjgwZTFiYTRhNjZkODY4NjdkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:31:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IklsbnJqaXFFTU9IMHRhQnhGcHhXUUE9PSIsInZhbHVlIjoiZnNsdWh5djVSVHlFZVBaSXo4aEtQQUlVaDF5bDUvZTlHR2cwd2lzckNuVkVCTUNUT3hoQzBKZnN3T3ZFajRtV3NnN0hlRExDbVFYYnFzcU5NcmhScGx0RGJMd2U2WC9KZ3hqRVlPbVdWVzdyWm03TXJrUHZvU2xTMUR5d1p3dkNrSXUvSksxMkIxQ0tsVHJkNGlKbmFmSjY3SkhGOXM1L0hFYlFMMk5aVHNncU80NC8rNFduYnlvVGdodUVKeEEyU2VOUjJnRmJNVzJSZVRFNGtYam1LRi9iM2pmRGZMMFhzWFBIMDZ1VlpnWitBOUZqeTY5YzNCVENvbXJVbllnOUhaekFNN0orNEt0QWpETlFVaW5kdnZ1MG4rMlllMXNXMmpISGxSWDZRcW1LWWJWVWtUcGpkZ3FUZHF2cFZRTWxhUS90SHNXOEdOQmJpdFdSQ2JvNjZSL2k4cndrb3pKT2hCa1l3U2FxZFJMRFJGcTAxQjAvV1QyaFN5emJ1RVVHemp0QndTRmtvL3RtOHZDOWN1MTlMbTh0S3o2OThjWTljeWtMQXdOeG5DRHRvY3BWdHZFRDZzVGEvUlZRTzR2azlQOGZvZGtxWjVBQnpObzFMRFJGa2EzNE8zYlBGMWlLUWlPU2U4dzEyOFJVZWVUNW9yVG03bXRXa25iSzBFeDQiLCJtYWMiOiI4ZTNiZGRhMzc2MGQ1NWEwOTU2OGNhNWYxYjhmYzg2OTJkZWNkNjBkYmFmYjhkZWQ2ZTc3MzBhZDNmMDkzNWNmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:31:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538465689\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1074193148 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1074193148\", {\"maxDepth\":0})</script>\n"}}