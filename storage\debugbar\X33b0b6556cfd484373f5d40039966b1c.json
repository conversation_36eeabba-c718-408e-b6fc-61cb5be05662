{"__meta": {"id": "X33b0b6556cfd484373f5d40039966b1c", "datetime": "2025-07-30 09:25:44", "utime": **********.859122, "method": "GET", "uri": "/finance/sales/contacts/lead/12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753867543.935238, "end": **********.859147, "duration": 0.9239091873168945, "duration_str": "924ms", "measures": [{"label": "Booting", "start": 1753867543.935238, "relative_start": 0, "end": **********.715022, "relative_end": **********.715022, "duration": 0.7797842025756836, "duration_str": "780ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.715044, "relative_start": 0.7798061370849609, "end": **********.85915, "relative_end": 2.86102294921875e-06, "duration": 0.1441059112548828, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46674720, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1023\" onclick=\"\">app/Http/Controllers/FinanceController.php:1023-1082</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.009770000000000001, "accumulated_duration_str": "9.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.809921, "duration": 0.00807, "duration_str": "8.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 82.6}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.836461, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 82.6, "width_percent": 8.802}, {"sql": "select * from `leads` where `id` = '12' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["12", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1049}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.841954, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1049", "source": "app/Http/Controllers/FinanceController.php:1049", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1049", "ajax": false, "filename": "FinanceController.php", "line": "1049"}, "connection": "radhe_same", "start_percent": 91.402, "width_percent": 8.598}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/12", "status_code": "<pre class=sf-dump id=sf-dump-389261025 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-389261025\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1621200887 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1621200887\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1741511530 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1741511530\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1972411264 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlFKVzVFWjQxdTVHN2NVZDRFOXU3dHc9PSIsInZhbHVlIjoiVktJYUVsaXllSnFEQ3VRdkdNNUJTZXFJSFA4TmE5Vk5kSmFYL0xxVFV2cURxSVdydnZVUk1tR0RUeDFCSkNrSDhLd3dkRWZseHRrbUk1aVcvRC9vaHJUUitEdnYwNmZEb1hIZXN6L2NsVFN2Uys2c2ltYis2ZHlnYVV3Sk81cWwraUt3TlNoMGxsMzZURHpVc0pjQVp2VnpIRG5SVEJxUFgxektWV3JqWXdrWnpXbnNHZEt0WWV1ZGRiRURjdE5WZFZHN0M5NEduWDRUUnRvM0tzMGNaMXE1VW4yM2xQNWVBY0paMWI5SkxLZVpDOU1YREhxVE1yYnltOUFhOW8wQktZQlFaWkovZVRxbDN4YUtqQmxmL3NXMWoyYm9KaTRRRDE4d205RHpxTC93SjB3ZVF5NFVuRlViYUxjK1hHQTVDbFZRTDJwRWY5MWYraEVtMVhLR1hCSDNXbTVTa2p0Ym5qSEVteGMwYm9RSnh1Mnhsd0oyblFkbnFDRjEvZ2tGb2txeFlZRmFkK21SdVRrM3U0QXV4bis0N1ZuaE1ERGhEdzFHbEJtUGJ4cDlTa3lMcHpUUGE4VTRQQ243dGdNU0xlWGdLTXhMWm1ab1VjV0ZMN09mcVJ6N1cveHNUbWZGeVk0VFhUdSsxRWdkSnhCQkIzcHlaZW9TVXVLRk10OGgiLCJtYWMiOiI3MTBiMzhhNzI2ZGUzYjJjMzYyNWQ4OTM0Zjk1ODkxNmQyMWQ5NmExYzY5ZjAyOWQyZTczZDc2MjVmODI1MzEwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImlaVk5jTE9NNDR0Zm9XejBVNHlSZ3c9PSIsInZhbHVlIjoiVE8wME9nL2VMMzViQW11aFE5T1daZThRdE9SOTU4ZHl5TkIvUWFDbkZ1bHFFRlBYZUhNVmhlVUMvNlpaTGRxcGROYU0wekludnplNWRHckY0d25USU53a3JsaXo4V0xlZzViajM1U0FiL25tRVVIS2NmMzRsMVcyLzNBNnV5NStqdkFaZTIvMGs3VllaTnNXY0lFRGs1T3BVWTZMVHhROWtqbDYyUGZZaDJ3RnlSYnF2a2NldUVEc3FNR3hrbHlUZCt1MUdCRDBOWHJyck9RMk00bUFnTG1jSDlPQnBnZWpUNHA1Wm1GQzBKMVpyZElSeTBnNDQyQkdqdW5qdktUTjJpeXpyRG5DZU1jYzdFUGNQZ0hWTDFWZWRyNkdLT0N6T1lENUFhdVRRdUE3T2lpVzA0UHRsMTVlWGQrNVdaVnZZTGRRd0FtOVV4aWwxcmJkV2pBQm85N0ZTZlJCRlIwN0pQb2pFc1JNbnV1TG5BQTJTTmtXWCtRRTJ4WVVCajJxM2I2M0tQelhjWjZYUTd0cFBkdGw1dVV2YUJMN1lhUUdZOTZRR05nRFZsbUlkK0FpbVQ0WHN2bmhiOXJjMHowMjJ3dWRsbDVDTHU4VDJPUUppaDlIcGNDMGRKVVdLMGpaVnRBUXg1Rk5JWVpJaDdFM0hGUllNTEtFWjVYVnFraE4iLCJtYWMiOiIyYmUzZTVjNzQ3NzU2NjlhYTY0MDg1YTcwMmM4NjhhZjVhYTFjMTczNWU0MGJlNGEyZmRlYjFhZjYyN2UzNGM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972411264\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1789095315 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:25:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ing1dDRYNmtWdDJYTjJDKzVtSUp6ZEE9PSIsInZhbHVlIjoiK1h6bGpVQ1lscWhzZHpGMFhuT1U1czd3R05IeWtjZHBPUjkwOUsxTDYxQTdXT3V0T09WZ0J1SHVNM1RYdTBXV2pZeWxnbEVIcWNtQ3QzY0E2UVdJZ1BXb1FvYzArS0FTVXdNalBoWHZkK1lvc1A3NVllRE1OMFk4RlpFQUpYYWhWa3Z1NzdwRkQ0VjdBcVg1by83NWdoUzhheDJzZ2JlN3Y3Rk5hcCs5eWh6MXdmTFpLUS9tZG0rSW5LT255dUxEaUx5bVg4akd4V0t2azZESHUwSTU3NStQcjhpeU5ZdmJNZEFIdHdrR1VITTJvY01DczBkSW5XZ1lKZDZvUkZyT2ljYWI0UnlRR0txV1Q5Q3RIdzlXMSt5U3NRSlQ0S0UrQ3VKZm9KeVJ5Nm9IekpaNjY0SzZndGoxQVRRYk5WayswaHNoNi82a2JiRGFsSXNTNEFxMklXVHlaczVJTUZqZVdsM3hHUmQzL3paWnpPcXhzRlE2TjhCMDJ4a25OY0xkaS9MblBZYUNJdFIyeTNWbmdQZEptNXRUZVNmT0ptYzQxaFIrczRUUGlXTzkyclYrUUkzM3hBRStRVWhqOXJrRmtDSS9Kb1ArZkV5UmFza3plRlA4WUhrOXNTV21kTmtESjlnejhEVVV2eU9idzJIRG1vM05SNlhuWGtKQUQza3kiLCJtYWMiOiJkMWU2Zjg5NDU4NzU5ZDYwODJhZjhiYTBiMjEzM2ZjYTYxNjM3Mjc1NjA2MmE0MTNjNTZkZGNkMWNjODg4NGZmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:25:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImhEL3dhVnJSQ3RRU3o3SU9rNklWaVE9PSIsInZhbHVlIjoidkk4TURQZmVDTmh3RmJ3eVllanpqOVRxYkJtd2N4aWpNdFpRK0pHVUxwWTdwV0UwOXNVdm9ZamJXRU5MTmFhcms5TGlTVnhqOEJxWUdmRHgreUpSNmNUVys3azNBdTU0SjZJVHVobnltVHlkeHVoSFlTTjRZOEI4ajdoaHpOekdFNFBuM0NlUFUzUnZxdlRRdzhuY1p3L3JZMUZMc2RSL1M4eDArZGRwUGtwd2NhNUdhcEZ5OGJnbk1xci9lT1FLRkFjWXB0eElDRmZIZzl0a0psVzYxUTVLQjRmZkxMTTNvK29mS2diTHlrQUtBTHRGSUJ2S3dFeng0cHRwOUtpa0x1VEhYWVBxNm9pRlJrMFp3Qms0NkNscHdEV2Fjb0NmNGpJTjhBcDZjZEQ1MmltNHFRbUgrVmFtNmhUU2pSZXNlZm03V0ZyY3ZZTEN3U1JEL0NBbkVxMVZuZWw0MUpWbXZSKzgwUjJFZ1FnRzJwUnpFQjB5R2tIellBcGVlZEE5L3RIQjBVWDUzZmwwVVFCbzJTajZUV3lhaHk4V1V3TGJ3WG5Nc3VxM2FyQ3Bqc2Y4VW9tSzBpekpOYUhYY3BGUy9WQVYzVHBxT2lhSEdLTHB5cG10YzZXRlY2aUJOeHE5NFNYVy9CS1hVT2xEeUZNanJRQW5tMER2dTlWQnhwM2QiLCJtYWMiOiJhMGJlYWQ1MTQ3YjkzYzcwMGFjMWM2YzI1NDZiNGRmNjg4YmVkNjExM2NhM2QzNDNlMGI5NWM2NGZmNDkwODJlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:25:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ing1dDRYNmtWdDJYTjJDKzVtSUp6ZEE9PSIsInZhbHVlIjoiK1h6bGpVQ1lscWhzZHpGMFhuT1U1czd3R05IeWtjZHBPUjkwOUsxTDYxQTdXT3V0T09WZ0J1SHVNM1RYdTBXV2pZeWxnbEVIcWNtQ3QzY0E2UVdJZ1BXb1FvYzArS0FTVXdNalBoWHZkK1lvc1A3NVllRE1OMFk4RlpFQUpYYWhWa3Z1NzdwRkQ0VjdBcVg1by83NWdoUzhheDJzZ2JlN3Y3Rk5hcCs5eWh6MXdmTFpLUS9tZG0rSW5LT255dUxEaUx5bVg4akd4V0t2azZESHUwSTU3NStQcjhpeU5ZdmJNZEFIdHdrR1VITTJvY01DczBkSW5XZ1lKZDZvUkZyT2ljYWI0UnlRR0txV1Q5Q3RIdzlXMSt5U3NRSlQ0S0UrQ3VKZm9KeVJ5Nm9IekpaNjY0SzZndGoxQVRRYk5WayswaHNoNi82a2JiRGFsSXNTNEFxMklXVHlaczVJTUZqZVdsM3hHUmQzL3paWnpPcXhzRlE2TjhCMDJ4a25OY0xkaS9MblBZYUNJdFIyeTNWbmdQZEptNXRUZVNmT0ptYzQxaFIrczRUUGlXTzkyclYrUUkzM3hBRStRVWhqOXJrRmtDSS9Kb1ArZkV5UmFza3plRlA4WUhrOXNTV21kTmtESjlnejhEVVV2eU9idzJIRG1vM05SNlhuWGtKQUQza3kiLCJtYWMiOiJkMWU2Zjg5NDU4NzU5ZDYwODJhZjhiYTBiMjEzM2ZjYTYxNjM3Mjc1NjA2MmE0MTNjNTZkZGNkMWNjODg4NGZmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:25:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImhEL3dhVnJSQ3RRU3o3SU9rNklWaVE9PSIsInZhbHVlIjoidkk4TURQZmVDTmh3RmJ3eVllanpqOVRxYkJtd2N4aWpNdFpRK0pHVUxwWTdwV0UwOXNVdm9ZamJXRU5MTmFhcms5TGlTVnhqOEJxWUdmRHgreUpSNmNUVys3azNBdTU0SjZJVHVobnltVHlkeHVoSFlTTjRZOEI4ajdoaHpOekdFNFBuM0NlUFUzUnZxdlRRdzhuY1p3L3JZMUZMc2RSL1M4eDArZGRwUGtwd2NhNUdhcEZ5OGJnbk1xci9lT1FLRkFjWXB0eElDRmZIZzl0a0psVzYxUTVLQjRmZkxMTTNvK29mS2diTHlrQUtBTHRGSUJ2S3dFeng0cHRwOUtpa0x1VEhYWVBxNm9pRlJrMFp3Qms0NkNscHdEV2Fjb0NmNGpJTjhBcDZjZEQ1MmltNHFRbUgrVmFtNmhUU2pSZXNlZm03V0ZyY3ZZTEN3U1JEL0NBbkVxMVZuZWw0MUpWbXZSKzgwUjJFZ1FnRzJwUnpFQjB5R2tIellBcGVlZEE5L3RIQjBVWDUzZmwwVVFCbzJTajZUV3lhaHk4V1V3TGJ3WG5Nc3VxM2FyQ3Bqc2Y4VW9tSzBpekpOYUhYY3BGUy9WQVYzVHBxT2lhSEdLTHB5cG10YzZXRlY2aUJOeHE5NFNYVy9CS1hVT2xEeUZNanJRQW5tMER2dTlWQnhwM2QiLCJtYWMiOiJhMGJlYWQ1MTQ3YjkzYzcwMGFjMWM2YzI1NDZiNGRmNjg4YmVkNjExM2NhM2QzNDNlMGI5NWM2NGZmNDkwODJlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:25:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1789095315\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1172074114 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172074114\", {\"maxDepth\":0})</script>\n"}}